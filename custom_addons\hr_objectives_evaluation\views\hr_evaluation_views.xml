<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Vue Tree des Évaluations -->
    <record id="view_hr_evaluation_tree" model="ir.ui.view">
        <field name="name">hr.evaluation.tree</field>
        <field name="model">hr.evaluation</field>
        <field name="arch" type="xml">
            <tree decoration-info="state=='draft'" decoration-warning="state=='self_evaluation'" decoration-success="state=='completed'">
                <field name="name"/>
                <field name="employee_id"/>
                <field name="evaluator_id"/>
                <field name="evaluation_type"/>
                <field name="evaluation_date"/>
                <field name="period_start"/>
                <field name="period_end"/>
                <field name="state" widget="badge"/>
                <field name="overall_score" widget="progressbar"/>
                <field name="recommendation"/>
            </tree>
        </field>
    </record>

    <!-- Vue Form des Évaluations -->
    <record id="view_hr_evaluation_form" model="ir.ui.view">
        <field name="name">hr.evaluation.form</field>
        <field name="model">hr.evaluation</field>
        <field name="arch" type="xml">
            <form>
                <header>
                    <button name="action_start_self_evaluation" string="Démarrer Auto-évaluation" type="object" 
                            attrs="{'invisible': [('state', '!=', 'draft')]}" class="btn-primary"/>
                    <button name="action_submit_self_evaluation" string="Soumettre Auto-évaluation" type="object" 
                            attrs="{'invisible': [('state', '!=', 'self_evaluation')]}" class="btn-primary"/>
                    <button name="action_complete_manager_evaluation" string="Terminer Évaluation Manager" type="object" 
                            attrs="{'invisible': [('state', '!=', 'manager_evaluation')]}" class="btn-primary"/>
                    <button name="action_complete_evaluation" string="Finaliser Évaluation" type="object" 
                            attrs="{'invisible': [('state', '!=', 'hr_review')]}" class="btn-success"/>
                    <button name="action_cancel" string="Annuler" type="object" 
                            attrs="{'invisible': [('state', 'in', ['completed', 'cancelled'])]}" class="btn-secondary"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,self_evaluation,manager_evaluation,hr_review,completed"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_objectives" type="object" class="oe_stat_button" icon="fa-bullseye">
                            <field name="objective_ids" widget="statinfo" string="Objectifs"/>
                        </button>
                    </div>
                    
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Nom de l'évaluation"/>
                        </h1>
                    </div>
                    
                    <group>
                        <group>
                            <field name="employee_id"/>
                            <field name="evaluator_id"/>
                            <field name="manager_id" readonly="1"/>
                            <field name="evaluation_type"/>
                        </group>
                        <group>
                            <field name="evaluation_date"/>
                            <field name="period_start"/>
                            <field name="period_end"/>
                            <field name="template_id"/>
                        </group>
                    </group>
                    
                    <notebook>
                        <page string="Critères d'Évaluation">
                            <field name="criteria_line_ids">
                                <tree editable="bottom">
                                    <field name="criteria_name"/>
                                    <field name="criteria_description"/>
                                    <field name="score" widget="progressbar"/>
                                    <field name="weight"/>
                                    <field name="comments"/>
                                </tree>
                            </field>
                            <group class="oe_subtotal_footer oe_right">
                                <field name="overall_score" widget="progressbar"/>
                            </group>
                        </page>
                        
                        <page string="Objectifs Évalués">
                            <field name="objective_ids">
                                <tree>
                                    <field name="name"/>
                                    <field name="state"/>
                                    <field name="progress" widget="progressbar"/>
                                    <field name="achievement_rate" widget="progressbar"/>
                                </tree>
                            </field>
                        </page>
                        
                        <page string="Commentaires">
                            <group>
                                <field name="employee_comments" string="Commentaires de l'employé"/>
                                <field name="manager_comments" string="Commentaires du manager"/>
                                <field name="hr_comments" string="Commentaires RH"/>
                            </group>
                        </page>
                        
                        <page string="Analyse">
                            <group>
                                <field name="strengths" string="Points forts"/>
                                <field name="improvement_areas" string="Axes d'amélioration"/>
                                <field name="development_plan" string="Plan de développement"/>
                            </group>
                            <group>
                                <field name="recommendation"/>
                                <field name="self_evaluation_score" readonly="1"/>
                                <field name="manager_score" readonly="1"/>
                            </group>
                        </page>
                        
                        <page string="Signatures">
                            <group>
                                <field name="employee_signature"/>
                                <field name="manager_signature"/>
                                <field name="hr_signature"/>
                            </group>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <!-- Vue Kanban des Évaluations -->
    <record id="view_hr_evaluation_kanban" model="ir.ui.view">
        <field name="name">hr.evaluation.kanban</field>
        <field name="model">hr.evaluation</field>
        <field name="arch" type="xml">
            <kanban default_group_by="state" class="o_kanban_small_column">
                <field name="name"/>
                <field name="employee_id"/>
                <field name="evaluation_date"/>
                <field name="overall_score"/>
                <field name="state"/>
                <field name="evaluation_type"/>
                <templates>
                    <t t-name="kanban-box">
                        <div class="oe_kanban_card oe_kanban_global_click">
                            <div class="oe_kanban_content">
                                <div class="o_kanban_record_top">
                                    <div class="o_kanban_record_headings">
                                        <strong class="o_kanban_record_title">
                                            <field name="name"/>
                                        </strong>
                                        <br/>
                                        <span class="o_kanban_record_subtitle">
                                            <field name="employee_id"/>
                                        </span>
                                    </div>
                                    <div class="o_kanban_record_top_right">
                                        <field name="evaluation_type" widget="badge"/>
                                    </div>
                                </div>
                                <div class="o_kanban_record_body">
                                    <div class="o_kanban_record_bottom">
                                        <div class="oe_kanban_bottom_left">
                                            <field name="evaluation_date"/>
                                        </div>
                                        <div class="oe_kanban_bottom_right">
                                            <field name="overall_score" widget="progressbar"/>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <!-- Vue Search des Évaluations -->
    <record id="view_hr_evaluation_search" model="ir.ui.view">
        <field name="name">hr.evaluation.search</field>
        <field name="model">hr.evaluation</field>
        <field name="arch" type="xml">
            <search>
                <field name="name"/>
                <field name="employee_id"/>
                <field name="evaluator_id"/>
                <field name="evaluation_type"/>
                <separator/>
                <filter string="Mes évaluations" name="my_evaluations" 
                        domain="[('employee_id.user_id', '=', uid)]"/>
                <filter string="À évaluer" name="to_evaluate" 
                        domain="[('evaluator_id.user_id', '=', uid)]"/>
                <separator/>
                <filter string="En cours" name="in_progress" 
                        domain="[('state', 'in', ['self_evaluation', 'manager_evaluation'])]"/>
                <filter string="Terminées" name="completed" 
                        domain="[('state', '=', 'completed')]"/>
                <separator/>
                <group expand="0" string="Grouper par">
                    <filter string="Employé" name="group_employee" context="{'group_by': 'employee_id'}"/>
                    <filter string="Évaluateur" name="group_evaluator" context="{'group_by': 'evaluator_id'}"/>
                    <filter string="Type" name="group_type" context="{'group_by': 'evaluation_type'}"/>
                    <filter string="État" name="group_state" context="{'group_by': 'state'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Action pour les Évaluations -->
    <record id="action_hr_evaluation" model="ir.actions.act_window">
        <field name="name">Évaluations</field>
        <field name="res_model">hr.evaluation</field>
        <field name="view_mode">kanban,tree,form</field>
        <field name="search_view_id" ref="view_hr_evaluation_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Créer une nouvelle évaluation
            </p>
            <p>
                Gérez les évaluations de performance de vos employés.
            </p>
        </field>
    </record>
</odoo>
