# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError
from datetime import datetime, timedelta


class HrObjectiveBulkWizard(models.TransientModel):
    _name = 'hr.objective.bulk.wizard'
    _description = 'Assistant de création d\'objectifs en masse'

    # Sélection des employés
    employee_ids = fields.Many2many(
        'hr.employee',
        string='Employés',
        required=True
    )
    department_ids = fields.Many2many(
        'hr.department',
        string='Départements'
    )
    
    # Configuration de l'objectif
    name = fields.Char(
        string='Nom de l\'objectif',
        required=True
    )
    description = fields.Html(
        string='Description'
    )
    
    # Dates
    start_date = fields.Date(
        string='Date de début',
        required=True,
        default=fields.Date.today
    )
    deadline = fields.Date(
        string='Date limite',
        required=True
    )
    
    # Configuration
    priority = fields.Selection([
        ('0', 'Faible'),
        ('1', 'Normal'),
        ('2', 'Élevé'),
        ('3', 'Urgent')
    ], string='Priorité', default='1')
    
    target_value = fields.Float(string='Valeur cible')
    unit = fields.Char(string='Unité de mesure')
    
    @api.onchange('department_ids')
    def _onchange_department_ids(self):
        """Filtre les employés par département"""
        if self.department_ids:
            employees = self.env['hr.employee'].search([
                ('department_id', 'in', self.department_ids.ids)
            ])
            self.employee_ids = employees
    
    def action_create_objectives(self):
        """Crée les objectifs pour les employés sélectionnés"""
        if not self.employee_ids:
            raise UserError(_("Veuillez sélectionner au moins un employé."))
        
        objectives = self.env['hr.objective']
        
        for employee in self.employee_ids:
            objective_vals = {
                'name': self.name,
                'description': self.description,
                'employee_id': employee.id,
                'start_date': self.start_date,
                'deadline': self.deadline,
                'priority': self.priority,
                'target_value': self.target_value,
                'unit': self.unit,
            }
            
            objective = self.env['hr.objective'].create(objective_vals)
            objectives |= objective
        
        # Retour vers la liste des objectifs créés
        return {
            'name': _('Objectifs créés'),
            'type': 'ir.actions.act_window',
            'res_model': 'hr.objective',
            'view_mode': 'tree,form',
            'domain': [('id', 'in', objectives.ids)],
            'context': {'create': False}
        }


class HrObjectiveProgressWizard(models.TransientModel):
    _name = 'hr.objective.progress.wizard'
    _description = 'Assistant de mise à jour de progression'

    objective_id = fields.Many2one(
        'hr.objective',
        string='Objectif',
        required=True
    )
    current_progress = fields.Float(
        string='Progression actuelle (%)',
        related='objective_id.progress',
        readonly=True
    )
    new_progress = fields.Float(
        string='Nouvelle progression (%)',
        required=True
    )
    current_value = fields.Float(
        string='Valeur actuelle'
    )
    comments = fields.Text(
        string='Commentaires'
    )
    
    @api.constrains('new_progress')
    def _check_progress(self):
        for record in self:
            if record.new_progress < 0 or record.new_progress > 100:
                raise UserError(_("La progression doit être comprise entre 0 et 100%."))
    
    def action_update_progress(self):
        """Met à jour la progression de l'objectif"""
        vals = {
            'progress': self.new_progress,
        }
        
        if self.current_value:
            vals['current_value'] = self.current_value
        
        self.objective_id.write(vals)
        
        # Ajouter un message dans le chatter
        if self.comments:
            self.objective_id.message_post(
                body=_("Progression mise à jour: %s%% - %s") % (self.new_progress, self.comments)
            )
        else:
            self.objective_id.message_post(
                body=_("Progression mise à jour: %s%%") % self.new_progress
            )
        
        # Mise à jour automatique du statut
        if self.new_progress >= 100 and self.objective_id.state == 'in_progress':
            self.objective_id.action_achieve()
        
        return {'type': 'ir.actions.act_window_close'}


class HrObjectiveReportWizard(models.TransientModel):
    _name = 'hr.objective.report.wizard'
    _description = 'Assistant de rapport d\'objectifs'

    # Filtres
    employee_ids = fields.Many2many(
        'hr.employee',
        string='Employés'
    )
    department_ids = fields.Many2many(
        'hr.department',
        string='Départements'
    )
    manager_ids = fields.Many2many(
        'hr.employee',
        string='Managers'
    )
    
    # Période
    date_from = fields.Date(
        string='Date de début',
        required=True,
        default=lambda self: fields.Date.today().replace(month=1, day=1)
    )
    date_to = fields.Date(
        string='Date de fin',
        required=True,
        default=fields.Date.today
    )
    
    # Options
    state_filter = fields.Selection([
        ('all', 'Tous les états'),
        ('active', 'Actifs seulement'),
        ('completed', 'Terminés seulement')
    ], string='Filtre d\'état', default='all')
    
    group_by = fields.Selection([
        ('employee', 'Par employé'),
        ('department', 'Par département'),
        ('manager', 'Par manager'),
        ('state', 'Par état')
    ], string='Grouper par', default='employee')
    
    def action_generate_report(self):
        """Génère le rapport d'objectifs"""
        domain = [
            ('start_date', '>=', self.date_from),
            ('deadline', '<=', self.date_to)
        ]
        
        if self.employee_ids:
            domain.append(('employee_id', 'in', self.employee_ids.ids))
        
        if self.department_ids:
            domain.append(('department_id', 'in', self.department_ids.ids))
        
        if self.manager_ids:
            domain.append(('manager_id', 'in', self.manager_ids.ids))
        
        if self.state_filter == 'active':
            domain.append(('state', 'in', ['draft', 'in_progress']))
        elif self.state_filter == 'completed':
            domain.append(('state', 'in', ['achieved', 'partially_achieved']))
        
        return {
            'name': _('Rapport d\'Objectifs'),
            'type': 'ir.actions.act_window',
            'res_model': 'hr.objective',
            'view_mode': 'tree,form,pivot,graph',
            'domain': domain,
            'context': {
                'group_by': self.group_by + '_id' if self.group_by != 'state' else 'state',
                'search_default_group_by': 1
            }
        }
