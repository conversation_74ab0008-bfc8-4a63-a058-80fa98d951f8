/* Styles pour le module Objectifs et Évaluations RH */

/* Kanban des objectifs */
.o_kanban_view .oe_kanban_card.oe_kanban_color_2 {
    background-color: #f8d7da !important;
    border-color: #f5c6cb !important;
}

/* Badges de statut */
.badge.o_field_badge[data-value="achieved"] {
    background-color: #28a745 !important;
    color: white !important;
}

.badge.o_field_badge[data-value="in_progress"] {
    background-color: #ffc107 !important;
    color: #212529 !important;
}

.badge.o_field_badge[data-value="not_achieved"] {
    background-color: #dc3545 !important;
    color: white !important;
}

.badge.o_field_badge[data-value="cancelled"] {
    background-color: #6c757d !important;
    color: white !important;
}

/* Badges de priorité */
.o_priority_star {
    color: #ffc107;
}

.o_priority_star.fa-star {
    color: #ffc107;
}

.o_priority_star.fa-star-o {
    color: #dee2e6;
}

/* Barres de progression */
.o_progressbar {
    height: 20px;
    border-radius: 10px;
    overflow: hidden;
}

.o_progressbar .o_progressbar_value {
    transition: width 0.3s ease;
}

/* Cartes d'objectifs */
.hr_objective_card {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    background-color: #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.hr_objective_card.overdue {
    border-left: 4px solid #dc3545;
}

.hr_objective_card.achieved {
    border-left: 4px solid #28a745;
}

.hr_objective_card.in_progress {
    border-left: 4px solid #ffc107;
}

/* Dashboard des objectifs */
.hr_objectives_dashboard {
    padding: 20px;
}

.hr_objectives_stats {
    display: flex;
    justify-content: space-around;
    margin-bottom: 30px;
}

.hr_stat_box {
    text-align: center;
    padding: 20px;
    border-radius: 8px;
    background-color: #f8f9fa;
    min-width: 150px;
}

.hr_stat_number {
    font-size: 2.5em;
    font-weight: bold;
    color: #495057;
}

.hr_stat_label {
    font-size: 0.9em;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Critères d'évaluation */
.evaluation_criteria_table {
    width: 100%;
    border-collapse: collapse;
}

.evaluation_criteria_table th,
.evaluation_criteria_table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #dee2e6;
}

.evaluation_criteria_table th {
    background-color: #f8f9fa;
    font-weight: 600;
}

.criteria_score_input {
    width: 80px;
    text-align: center;
}

/* Niveaux de performance */
.performance_levels {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 10px;
    margin: 20px 0;
}

.performance_level {
    text-align: center;
    padding: 15px 10px;
    border-radius: 8px;
    border: 2px solid transparent;
}

.performance_level.level_1 {
    background-color: #f8d7da;
    border-color: #dc3545;
}

.performance_level.level_2 {
    background-color: #fff3cd;
    border-color: #ffc107;
}

.performance_level.level_3 {
    background-color: #d1ecf1;
    border-color: #17a2b8;
}

.performance_level.level_4 {
    background-color: #d4edda;
    border-color: #28a745;
}

.performance_level.level_5 {
    background-color: #e2e3e5;
    border-color: #6c757d;
}

.performance_level h4 {
    margin: 0 0 10px 0;
    font-size: 1.1em;
}

.performance_level p {
    margin: 0;
    font-size: 0.9em;
}

/* Formulaire d'auto-évaluation */
.self_evaluation_form {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

.criteria_evaluation_section {
    margin-bottom: 30px;
    padding: 20px;
    border: 1px solid #dee2e6;
    border-radius: 8px;
}

.criteria_evaluation_section h3 {
    margin-top: 0;
    color: #495057;
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
}

/* Responsive design */
@media (max-width: 768px) {
    .hr_objectives_stats {
        flex-direction: column;
        gap: 15px;
    }
    
    .performance_levels {
        grid-template-columns: 1fr;
    }
    
    .evaluation_criteria_table {
        font-size: 0.9em;
    }
    
    .evaluation_criteria_table th,
    .evaluation_criteria_table td {
        padding: 8px;
    }
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.hr_objective_card,
.criteria_evaluation_section {
    animation: fadeIn 0.3s ease-out;
}

/* Tooltips personnalisés */
.hr_tooltip {
    position: relative;
    cursor: help;
}

.hr_tooltip:hover::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: #333;
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 0.8em;
    white-space: nowrap;
    z-index: 1000;
}

/* Boutons d'action */
.hr_action_buttons {
    display: flex;
    gap: 10px;
    margin-top: 15px;
}

.hr_action_buttons .btn {
    flex: 1;
    min-width: 120px;
}

/* Indicateurs de tendance */
.performance_trend_improving {
    color: #28a745;
}

.performance_trend_stable {
    color: #17a2b8;
}

.performance_trend_declining {
    color: #dc3545;
}

.performance_trend_new {
    color: #6c757d;
}
