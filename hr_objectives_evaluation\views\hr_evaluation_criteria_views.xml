<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Vue Tree des Critères -->
    <record id="view_hr_evaluation_criteria_tree" model="ir.ui.view">
        <field name="name">hr.evaluation.criteria.tree</field>
        <field name="model">hr.evaluation.criteria</field>
        <field name="type">tree</field>
        <field name="arch" type="xml">
            <tree>
                <field name="sequence" widget="handle"/>
                <field name="name"/>
                <field name="category_id"/>
                <field name="criteria_type"/>
                <field name="scale_type"/>
                <field name="default_weight"/>
                <field name="active"/>
            </tree>
        </field>
    </record>

    <!-- Vue Form des Critères -->
    <record id="view_hr_evaluation_criteria_form" model="ir.ui.view">
        <field name="name">hr.evaluation.criteria.form</field>
        <field name="model">hr.evaluation.criteria</field>
        <field name="type">form</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_templates" type="object" class="oe_stat_button" icon="fa-file-text">
                            <field name="template_ids" widget="statinfo" string="Modèles"/>
                        </button>
                    </div>
                    
                    <widget name="web_ribbon" title="Archivé" bg_color="bg-danger" attrs="{'invisible': [('active', '=', True)]}"/>
                    
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Nom du critère"/>
                        </h1>
                    </div>
                    
                    <group>
                        <group>
                            <field name="category_id"/>
                            <field name="criteria_type"/>
                            <field name="scale_type"/>
                            <field name="default_weight"/>
                        </group>
                        <group>
                            <field name="sequence"/>
                            <field name="active"/>
                        </group>
                    </group>
                    
                    <group>
                        <field name="description" placeholder="Description du critère"/>
                    </group>
                    
                    <notebook>
                        <page string="Niveaux de Performance">
                            <group>
                                <field name="level_1_description" string="Niveau 1 - Insuffisant"/>
                                <field name="level_2_description" string="Niveau 2 - En développement"/>
                                <field name="level_3_description" string="Niveau 3 - Satisfaisant"/>
                                <field name="level_4_description" string="Niveau 4 - Bon"/>
                                <field name="level_5_description" string="Niveau 5 - Excellent"/>
                            </group>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Vue Search des Critères -->
    <record id="view_hr_evaluation_criteria_search" model="ir.ui.view">
        <field name="name">hr.evaluation.criteria.search</field>
        <field name="model">hr.evaluation.criteria</field>
        <field name="type">search</field>
        <field name="arch" type="xml">
            <search>
                <field name="name"/>
                <field name="category_id"/>
                <field name="criteria_type"/>
                <separator/>
                <filter string="Actifs" name="active" domain="[('active', '=', True)]"/>
                <filter string="Archivés" name="inactive" domain="[('active', '=', False)]"/>
                <separator/>
                <group expand="0" string="Grouper par">
                    <filter string="Catégorie" name="group_category" context="{'group_by': 'category_id'}"/>
                    <filter string="Type" name="group_type" context="{'group_by': 'criteria_type'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Action pour les Critères -->
    <record id="action_hr_evaluation_criteria" model="ir.actions.act_window">
        <field name="name">Critères d'Évaluation</field>
        <field name="res_model">hr.evaluation.criteria</field>
        <field name="view_mode">tree,form</field>
        <field name="search_view_id" ref="view_hr_evaluation_criteria_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Créer un nouveau critère d'évaluation
            </p>
            <p>
                Définissez les critères utilisés pour évaluer vos employés.
            </p>
        </field>
    </record>

    <!-- Vue Tree des Catégories de Critères -->
    <record id="view_hr_evaluation_criteria_category_tree" model="ir.ui.view">
        <field name="name">hr.evaluation.criteria.category.tree</field>
        <field name="model">hr.evaluation.criteria.category</field>
        <field name="type">tree</field>
        <field name="arch" type="xml">
            <tree>
                <field name="sequence" widget="handle"/>
                <field name="name"/>
                <field name="criteria_count"/>
                <field name="active"/>
            </tree>
        </field>
    </record>

    <!-- Vue Form des Catégories de Critères -->
    <record id="view_hr_evaluation_criteria_category_form" model="ir.ui.view">
        <field name="name">hr.evaluation.criteria.category.form</field>
        <field name="model">hr.evaluation.criteria.category</field>
        <field name="type">form</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_criteria" type="object" class="oe_stat_button" icon="fa-list">
                            <field name="criteria_count" widget="statinfo" string="Critères"/>
                        </button>
                    </div>
                    
                    <widget name="web_ribbon" title="Archivé" bg_color="bg-danger" attrs="{'invisible': [('active', '=', True)]}"/>
                    
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Nom de la catégorie"/>
                        </h1>
                    </div>
                    
                    <group>
                        <group>
                            <field name="sequence"/>
                            <field name="color" widget="color"/>
                        </group>
                        <group>
                            <field name="active"/>
                        </group>
                    </group>
                    
                    <group>
                        <field name="description" placeholder="Description de la catégorie"/>
                    </group>
                    
                    <notebook>
                        <page string="Critères">
                            <field name="criteria_ids">
                                <tree>
                                    <field name="name"/>
                                    <field name="criteria_type"/>
                                    <field name="default_weight"/>
                                    <field name="active"/>
                                </tree>
                            </field>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Action pour les Catégories de Critères -->
    <record id="action_hr_evaluation_criteria_category" model="ir.actions.act_window">
        <field name="name">Catégories de Critères</field>
        <field name="res_model">hr.evaluation.criteria.category</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Créer une nouvelle catégorie
            </p>
            <p>
                Organisez vos critères d'évaluation par catégories.
            </p>
        </field>
    </record>
</odoo>
