# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
from datetime import datetime, timedelta


class HrObjective(models.Model):
    _name = 'hr.objective'
    _description = 'Objectif RH'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'priority desc, deadline asc'

    name = fields.Char(
        string='Nom de l\'objectif',
        required=True,
        tracking=True
    )
    description = fields.Html(
        string='Description',
        tracking=True
    )
    employee_id = fields.Many2one(
        'hr.employee',
        string='Employé',
        required=True,
        tracking=True
    )
    manager_id = fields.Many2one(
        'hr.employee',
        string='Manager',
        related='employee_id.parent_id',
        store=True
    )
    department_id = fields.Many2one(
        'hr.department',
        string='Département',
        related='employee_id.department_id',
        store=True
    )
    
    # Dates
    start_date = fields.Date(
        string='Date de début',
        required=True,
        default=fields.Date.today,
        tracking=True
    )
    deadline = fields.Date(
        string='Date limite',
        required=True,
        tracking=True
    )
    
    # SMART Criteria
    specific = fields.Text(string='Spécifique')
    measurable = fields.Text(string='Mesurable')
    achievable = fields.Text(string='Atteignable')
    relevant = fields.Text(string='Pertinent')
    time_bound = fields.Text(string='Temporellement défini')
    
    # Progress and Status
    state = fields.Selection([
        ('draft', 'Brouillon'),
        ('in_progress', 'En cours'),
        ('achieved', 'Atteint'),
        ('partially_achieved', 'Partiellement atteint'),
        ('not_achieved', 'Non atteint'),
        ('cancelled', 'Annulé')
    ], string='État', default='draft', tracking=True)
    
    progress = fields.Float(
        string='Progression (%)',
        default=0.0,
        help="Progression de l'objectif en pourcentage"
    )
    
    priority = fields.Selection([
        ('0', 'Faible'),
        ('1', 'Normal'),
        ('2', 'Élevé'),
        ('3', 'Urgent')
    ], string='Priorité', default='1')
    
    # Evaluation
    target_value = fields.Float(string='Valeur cible')
    current_value = fields.Float(string='Valeur actuelle')
    unit = fields.Char(string='Unité de mesure')
    
    # Relations
    evaluation_ids = fields.One2many(
        'hr.evaluation',
        'objective_id',
        string='Évaluations'
    )
    
    # Computed fields
    days_remaining = fields.Integer(
        string='Jours restants',
        compute='_compute_days_remaining'
    )
    is_overdue = fields.Boolean(
        string='En retard',
        compute='_compute_is_overdue'
    )
    achievement_rate = fields.Float(
        string='Taux de réalisation (%)',
        compute='_compute_achievement_rate'
    )
    
    @api.depends('deadline')
    def _compute_days_remaining(self):
        for record in self:
            if record.deadline:
                delta = record.deadline - fields.Date.today()
                record.days_remaining = delta.days
            else:
                record.days_remaining = 0
    
    @api.depends('deadline', 'state')
    def _compute_is_overdue(self):
        today = fields.Date.today()
        for record in self:
            record.is_overdue = (
                record.deadline and 
                record.deadline < today and 
                record.state not in ['achieved', 'cancelled']
            )
    
    @api.depends('current_value', 'target_value')
    def _compute_achievement_rate(self):
        for record in self:
            if record.target_value:
                record.achievement_rate = (record.current_value / record.target_value) * 100
            else:
                record.achievement_rate = record.progress
    
    @api.constrains('start_date', 'deadline')
    def _check_dates(self):
        for record in self:
            if record.start_date and record.deadline:
                if record.start_date > record.deadline:
                    raise ValidationError(_("La date de début ne peut pas être postérieure à la date limite."))
    
    def action_start(self):
        """Démarre l'objectif"""
        self.write({'state': 'in_progress'})
        self.message_post(body=_("L'objectif a été démarré."))
    
    def action_achieve(self):
        """Marque l'objectif comme atteint"""
        self.write({
            'state': 'achieved',
            'progress': 100.0
        })
        self.message_post(body=_("L'objectif a été atteint !"))
    
    def action_cancel(self):
        """Annule l'objectif"""
        self.write({'state': 'cancelled'})
        self.message_post(body=_("L'objectif a été annulé."))
    
    @api.model
    def create(self, vals):
        """Override create to send notification"""
        objective = super().create(vals)
        objective.message_subscribe([objective.employee_id.user_id.partner_id.id])
        if objective.manager_id and objective.manager_id.user_id:
            objective.message_subscribe([objective.manager_id.user_id.partner_id.id])
        return objective
