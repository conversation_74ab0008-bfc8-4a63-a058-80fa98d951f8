#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os

# Ajouter le chemin d'Odoo au PYTHONPATH
odoo_path = r"C:\Program Files\Odoo 18.0.20250709\server"
sys.path.insert(0, odoo_path)

try:
    import odoo
    from odoo import api, SUPERUSER_ID
    from odoo.tools import config
    
    # Configuration de la base de données
    config['db_host'] = 'localhost'
    config['db_port'] = 5432
    config['db_user'] = 'postgres'
    config['db_password'] = ''
    
    # Nom de la base de données (remplacez par le nom de votre DB)
    database_name = 'odoo'
    
    print("Tentative de connexion à la base de données...")
    
    # Initialiser Odoo
    odoo.tools.config.parse_config([])
    
    # Se connecter à la base de données
    with api.Environment.manage():
        registry = odoo.registry(database_name)
        with registry.cursor() as cr:
            env = api.Environment(cr, SUPERUSER_ID, {})
            
            # Chercher l'utilisateur admin
            admin_user = env['res.users'].search([('login', '=', '<EMAIL>')], limit=1)
            
            if admin_user:
                # Nouveau mot de passe
                new_password = 'admin123'
                admin_user.write({'password': new_password})
                cr.commit()
                
                print(f"✅ Mot de passe réinitialisé avec succès!")
                print(f"📧 Email: <EMAIL>")
                print(f"🔑 Nouveau mot de passe: {new_password}")
            else:
                # Créer un nouvel utilisateur admin
                print("Utilisateur non trouvé, création d'un nouvel administrateur...")
                
                new_admin = env['res.users'].create({
                    'name': 'Administrateur',
                    'login': '<EMAIL>',
                    'email': '<EMAIL>',
                    'password': 'admin123',
                    'groups_id': [(6, 0, [env.ref('base.group_system').id])]
                })
                cr.commit()
                
                print(f"✅ Nouvel administrateur créé!")
                print(f"📧 Email: <EMAIL>")
                print(f"🔑 Mot de passe: admin123")

except Exception as e:
    print(f"❌ Erreur: {e}")
    print("\n🔧 Solution alternative:")
    print("1. Arrêtez le service Odoo")
    print("2. Utilisez la commande: odoo-bin -d votre_db --init=base --stop-after-init")
    print("3. Cela créera un utilisateur admin avec le mot de passe 'admin'")
