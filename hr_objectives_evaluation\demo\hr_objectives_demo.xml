<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Données de démonstration pour les objectifs et évaluations -->
    
    <!-- Objectifs de démonstration -->
    <record id="demo_objective_1" model="hr.objective">
        <field name="name">Améliorer les compétences en Python</field>
        <field name="description"><![CDATA[
            <p>Objectif de développement des compétences techniques en programmation Python.</p>
            <ul>
                <li>Suivre une formation Python avancée</li>
                <li>Réaliser un projet pratique</li>
                <li>Obtenir une certification</li>
            </ul>
        ]]></field>
        <field name="employee_id" ref="hr.employee_admin"/>
        <field name="start_date" eval="(DateTime.today() - timedelta(days=30)).strftime('%Y-%m-%d')"/>
        <field name="deadline" eval="(DateTime.today() + timedelta(days=90)).strftime('%Y-%m-%d')"/>
        <field name="state">in_progress</field>
        <field name="progress">45.0</field>
        <field name="priority">2</field>
        <field name="target_value">100.0</field>
        <field name="current_value">45.0</field>
        <field name="unit">%</field>
        <field name="specific">Améliorer les compétences en programmation Python</field>
        <field name="measurable">Obtenir une certification Python avec un score minimum de 80%</field>
        <field name="achievable">Formation disponible, temps alloué pour l'apprentissage</field>
        <field name="relevant">Compétence clé pour les projets futurs</field>
        <field name="time_bound">3 mois pour compléter la formation et obtenir la certification</field>
    </record>

    <record id="demo_objective_2" model="hr.objective">
        <field name="name">Augmenter la satisfaction client</field>
        <field name="description"><![CDATA[
            <p>Améliorer la satisfaction client de notre département.</p>
            <p>Actions prévues :</p>
            <ul>
                <li>Mettre en place un système de feedback</li>
                <li>Réduire le temps de réponse</li>
                <li>Former l'équipe au service client</li>
            </ul>
        ]]></field>
        <field name="employee_id" ref="hr.employee_admin"/>
        <field name="start_date" eval="(DateTime.today() - timedelta(days=60)).strftime('%Y-%m-%d')"/>
        <field name="deadline" eval="(DateTime.today() + timedelta(days=60)).strftime('%Y-%m-%d')"/>
        <field name="state">in_progress</field>
        <field name="progress">70.0</field>
        <field name="priority">3</field>
        <field name="target_value">85.0</field>
        <field name="current_value">78.0</field>
        <field name="unit">% satisfaction</field>
    </record>

    <record id="demo_objective_3" model="hr.objective">
        <field name="name">Réduire les coûts opérationnels</field>
        <field name="description"><![CDATA[
            <p>Objectif de réduction des coûts opérationnels du département.</p>
            <p>Stratégies :</p>
            <ul>
                <li>Optimiser les processus</li>
                <li>Négocier avec les fournisseurs</li>
                <li>Automatiser certaines tâches</li>
            </ul>
        ]]></field>
        <field name="employee_id" ref="hr.employee_admin"/>
        <field name="start_date" eval="(DateTime.today() - timedelta(days=90)).strftime('%Y-%m-%d')"/>
        <field name="deadline" eval="(DateTime.today() + timedelta(days=30)).strftime('%Y-%m-%d')"/>
        <field name="state">achieved</field>
        <field name="progress">100.0</field>
        <field name="priority">2</field>
        <field name="target_value">15.0</field>
        <field name="current_value">18.0</field>
        <field name="unit">% réduction</field>
    </record>

    <!-- Évaluation de démonstration -->
    <record id="demo_evaluation_1" model="hr.evaluation">
        <field name="name">Évaluation Annuelle 2024 - Admin</field>
        <field name="employee_id" ref="hr.employee_admin"/>
        <field name="evaluator_id" ref="hr.employee_admin"/>
        <field name="template_id" ref="template_annual_evaluation"/>
        <field name="evaluation_type">annual</field>
        <field name="evaluation_date" eval="DateTime.today().strftime('%Y-%m-%d')"/>
        <field name="period_start" eval="(DateTime.today() - timedelta(days=365)).strftime('%Y-%m-%d')"/>
        <field name="period_end" eval="(DateTime.today() - timedelta(days=1)).strftime('%Y-%m-%d')"/>
        <field name="state">completed</field>
        <field name="employee_comments"><![CDATA[
            <p>Cette année a été très enrichissante. J'ai pu développer mes compétences techniques 
            et contribuer significativement aux projets de l'équipe.</p>
            <p>Points forts que je retiens :</p>
            <ul>
                <li>Amélioration de mes compétences en Python</li>
                <li>Bonne collaboration avec l'équipe</li>
                <li>Respect des délais sur tous les projets</li>
            </ul>
        ]]></field>
        <field name="manager_comments"><![CDATA[
            <p>Excellente performance cette année. L'employé a montré une grande motivation 
            et des résultats remarquables.</p>
            <p>Axes d'amélioration pour l'année prochaine :</p>
            <ul>
                <li>Développer les compétences de leadership</li>
                <li>Prendre plus d'initiatives</li>
            </ul>
        ]]></field>
        <field name="strengths"><![CDATA[
            <ul>
                <li>Excellentes compétences techniques</li>
                <li>Très bon esprit d'équipe</li>
                <li>Capacité d'adaptation remarquable</li>
                <li>Respect des délais</li>
            </ul>
        ]]></field>
        <field name="improvement_areas"><![CDATA[
            <ul>
                <li>Développer les compétences de présentation</li>
                <li>Améliorer la communication écrite</li>
                <li>Prendre plus d'initiatives de leadership</li>
            </ul>
        ]]></field>
        <field name="development_plan"><![CDATA[
            <p>Plan de développement pour l'année prochaine :</p>
            <ul>
                <li>Formation en leadership (Q1)</li>
                <li>Cours de communication (Q2)</li>
                <li>Mentorat d'un junior (Q3-Q4)</li>
                <li>Participation à des conférences techniques</li>
            </ul>
        ]]></field>
        <field name="recommendation">exceed</field>
        <field name="self_evaluation_score">4.2</field>
        <field name="manager_score">4.5</field>
        <field name="employee_signature">True</field>
        <field name="manager_signature">True</field>
    </record>

    <!-- Lignes de critères pour l'évaluation de démonstration -->
    <record id="demo_evaluation_criteria_line_1" model="hr.evaluation.criteria.line">
        <field name="evaluation_id" ref="demo_evaluation_1"/>
        <field name="criteria_id" ref="criteria_technical_skills"/>
        <field name="score">4.5</field>
        <field name="weight">1.0</field>
        <field name="comments">Excellentes compétences techniques, toujours à jour avec les nouvelles technologies.</field>
    </record>

    <record id="demo_evaluation_criteria_line_2" model="hr.evaluation.criteria.line">
        <field name="evaluation_id" ref="demo_evaluation_1"/>
        <field name="criteria_id" ref="criteria_communication"/>
        <field name="score">4.0</field>
        <field name="weight">1.0</field>
        <field name="comments">Bonne communication, peut s'améliorer dans les présentations formelles.</field>
    </record>

    <record id="demo_evaluation_criteria_line_3" model="hr.evaluation.criteria.line">
        <field name="evaluation_id" ref="demo_evaluation_1"/>
        <field name="criteria_id" ref="criteria_teamwork"/>
        <field name="score">4.8</field>
        <field name="weight">1.0</field>
        <field name="comments">Excellent esprit d'équipe, toujours prêt à aider les collègues.</field>
    </record>

    <record id="demo_evaluation_criteria_line_4" model="hr.evaluation.criteria.line">
        <field name="evaluation_id" ref="demo_evaluation_1"/>
        <field name="criteria_id" ref="criteria_quality"/>
        <field name="score">4.3</field>
        <field name="weight">1.5</field>
        <field name="comments">Travail de haute qualité, attention aux détails remarquable.</field>
    </record>

    <record id="demo_evaluation_criteria_line_5" model="hr.evaluation.criteria.line">
        <field name="evaluation_id" ref="demo_evaluation_1"/>
        <field name="criteria_id" ref="criteria_productivity"/>
        <field name="score">4.6</field>
        <field name="weight">1.5</field>
        <field name="comments">Très productif, dépasse régulièrement les objectifs fixés.</field>
    </record>

    <record id="demo_evaluation_criteria_line_6" model="hr.evaluation.criteria.line">
        <field name="evaluation_id" ref="demo_evaluation_1"/>
        <field name="criteria_id" ref="criteria_innovation"/>
        <field name="score">4.0</field>
        <field name="weight">0.8</field>
        <field name="comments">Propose régulièrement des améliorations, esprit créatif.</field>
    </record>

    <!-- Liaison des objectifs avec l'évaluation -->
    <record id="demo_evaluation_objective_rel_1" model="hr.evaluation">
        <field name="id" ref="demo_evaluation_1"/>
        <field name="objective_ids" eval="[(6, 0, [ref('demo_objective_1'), ref('demo_objective_2'), ref('demo_objective_3')])]"/>
    </record>
</odoo>
