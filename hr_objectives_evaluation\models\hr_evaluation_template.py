# -*- coding: utf-8 -*-

from odoo import models, fields, api, _


class HrEvaluationTemplate(models.Model):
    _name = 'hr.evaluation.template'
    _description = 'Modèle d\'évaluation'
    _order = 'name'

    name = fields.Char(
        string='Nom du modèle',
        required=True
    )
    description = fields.Text(
        string='Description'
    )
    active = fields.Boolean(
        string='Actif',
        default=True
    )
    
    # Type de modèle
    template_type = fields.Selection([
        ('annual', 'Évaluation annuelle'),
        ('mid_year', 'Évaluation mi-année'),
        ('quarterly', 'Évaluation trimestrielle'),
        ('probation', 'Période d\'essai'),
        ('project', 'Fin de projet'),
        ('360', 'Évaluation 360°')
    ], string='Type de modèle', required=True, default='annual')
    
    # Critères
    criteria_ids = fields.Many2many(
        'hr.evaluation.criteria',
        'evaluation_template_criteria_rel',
        'template_id',
        'criteria_id',
        string='Critères d\'évaluation'
    )
    
    # Configuration
    allow_self_evaluation = fields.Boolean(
        string='Autoriser l\'auto-évaluation',
        default=True
    )
    require_manager_approval = fields.Boolean(
        string='Approbation manager requise',
        default=True
    )
    require_hr_review = fields.Boolean(
        string='Révision RH requise',
        default=False
    )
    
    # Départements et postes applicables
    department_ids = fields.Many2many(
        'hr.department',
        string='Départements applicables',
        help="Si vide, applicable à tous les départements"
    )
    job_ids = fields.Many2many(
        'hr.job',
        string='Postes applicables',
        help="Si vide, applicable à tous les postes"
    )
    
    # Périodicité
    frequency = fields.Selection([
        ('annual', 'Annuelle'),
        ('semi_annual', 'Semestrielle'),
        ('quarterly', 'Trimestrielle'),
        ('monthly', 'Mensuelle'),
        ('on_demand', 'À la demande')
    ], string='Fréquence', default='annual')
    
    # Instructions
    instructions = fields.Html(
        string='Instructions',
        help="Instructions pour l'évaluateur et l'évalué"
    )
    
    # Statistiques
    evaluation_count = fields.Integer(
        string='Nombre d\'évaluations',
        compute='_compute_evaluation_count'
    )
    
    @api.depends('criteria_ids')
    def _compute_evaluation_count(self):
        for template in self:
            template.evaluation_count = self.env['hr.evaluation'].search_count([
                ('template_id', '=', template.id)
            ])
    
    def action_view_evaluations(self):
        """Action pour voir les évaluations utilisant ce modèle"""
        return {
            'name': _('Évaluations'),
            'type': 'ir.actions.act_window',
            'res_model': 'hr.evaluation',
            'view_mode': 'tree,form',
            'domain': [('template_id', '=', self.id)],
            'context': {'default_template_id': self.id}
        }
    
    def copy(self, default=None):
        """Override copy to add (copy) suffix"""
        if default is None:
            default = {}
        default['name'] = _('%s (Copie)') % self.name
        return super().copy(default)


class HrEvaluationTemplateCriteria(models.Model):
    _name = 'hr.evaluation.template.criteria'
    _description = 'Critère de modèle d\'évaluation'
    _order = 'sequence, criteria_id'

    template_id = fields.Many2one(
        'hr.evaluation.template',
        string='Modèle',
        required=True,
        ondelete='cascade'
    )
    criteria_id = fields.Many2one(
        'hr.evaluation.criteria',
        string='Critère',
        required=True
    )
    sequence = fields.Integer(
        string='Séquence',
        default=10
    )
    weight = fields.Float(
        string='Poids',
        default=1.0,
        help="Poids de ce critère dans l'évaluation globale"
    )
    required = fields.Boolean(
        string='Obligatoire',
        default=True
    )
    
    # Champs liés
    criteria_name = fields.Char(
        related='criteria_id.name',
        string='Nom du critère'
    )
    criteria_type = fields.Selection(
        related='criteria_id.criteria_type',
        string='Type'
    )
