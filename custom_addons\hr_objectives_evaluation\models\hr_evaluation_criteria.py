# -*- coding: utf-8 -*-

from odoo import models, fields, api, _


class HrEvaluationCriteria(models.Model):
    _name = 'hr.evaluation.criteria'
    _description = 'Critère d\'évaluation'
    _order = 'sequence, name'

    name = fields.Char(
        string='Nom du critère',
        required=True,
        translate=True
    )
    description = fields.Text(
        string='Description',
        translate=True
    )
    sequence = fields.Integer(
        string='Séquence',
        default=10
    )
    active = fields.Boolean(
        string='Actif',
        default=True
    )
    
    # Catégorie
    category_id = fields.Many2one(
        'hr.evaluation.criteria.category',
        string='Catégorie'
    )
    
    # Poids par défaut
    default_weight = fields.Float(
        string='Poids par défaut',
        default=1.0,
        help="Poids par défaut de ce critère dans les évaluations"
    )
    
    # Type de critère
    criteria_type = fields.Selection([
        ('competency', 'Compétence'),
        ('behavior', 'Comportement'),
        ('objective', 'Objectif'),
        ('skill', 'Compétence technique'),
        ('leadership', 'Leadership'),
        ('teamwork', 'Travail d\'équipe'),
        ('communication', 'Communication'),
        ('innovation', 'Innovation'),
        ('quality', 'Qualité'),
        ('productivity', 'Productivité')
    ], string='Type de critère', default='competency')
    
    # Échelle d'évaluation
    scale_type = fields.Selection([
        ('numeric', 'Numérique (0-5)'),
        ('percentage', 'Pourcentage (0-100%)'),
        ('rating', 'Notation (A-F)'),
        ('boolean', 'Oui/Non')
    ], string='Type d\'échelle', default='numeric')
    
    # Niveaux de performance
    level_1_description = fields.Text(string='Niveau 1 - Insuffisant')
    level_2_description = fields.Text(string='Niveau 2 - En développement')
    level_3_description = fields.Text(string='Niveau 3 - Satisfaisant')
    level_4_description = fields.Text(string='Niveau 4 - Bon')
    level_5_description = fields.Text(string='Niveau 5 - Excellent')
    
    # Relations
    template_ids = fields.Many2many(
        'hr.evaluation.template',
        'evaluation_template_criteria_rel',
        'criteria_id',
        'template_id',
        string='Modèles d\'évaluation'
    )


class HrEvaluationCriteriaCategory(models.Model):
    _name = 'hr.evaluation.criteria.category'
    _description = 'Catégorie de critère d\'évaluation'
    _order = 'sequence, name'

    name = fields.Char(
        string='Nom de la catégorie',
        required=True,
        translate=True
    )
    description = fields.Text(
        string='Description',
        translate=True
    )
    sequence = fields.Integer(
        string='Séquence',
        default=10
    )
    active = fields.Boolean(
        string='Actif',
        default=True
    )
    color = fields.Integer(
        string='Couleur',
        default=0
    )
    
    # Relations
    criteria_ids = fields.One2many(
        'hr.evaluation.criteria',
        'category_id',
        string='Critères'
    )
    criteria_count = fields.Integer(
        string='Nombre de critères',
        compute='_compute_criteria_count'
    )
    
    @api.depends('criteria_ids')
    def _compute_criteria_count(self):
        for category in self:
            category.criteria_count = len(category.criteria_ids)
