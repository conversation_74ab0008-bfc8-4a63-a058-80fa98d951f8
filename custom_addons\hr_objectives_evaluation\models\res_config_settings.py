# -*- coding: utf-8 -*-

from odoo import models, fields, api


class ResConfigSettings(models.TransientModel):
    _inherit = 'res.config.settings'

    # Configuration des objectifs
    default_objective_duration = fields.Integer(
        string='Durée par défaut des objectifs (jours)',
        default=365,
        config_parameter='hr_objectives_evaluation.default_objective_duration'
    )
    
    auto_create_objectives = fields.Boolean(
        string='Création automatique d\'objectifs',
        config_parameter='hr_objectives_evaluation.auto_create_objectives'
    )
    
    # Configuration des évaluations
    default_evaluation_template_id = fields.Many2one(
        'hr.evaluation.template',
        string='Modèle d\'évaluation par défaut',
        config_parameter='hr_objectives_evaluation.default_evaluation_template_id'
    )
    
    evaluation_frequency = fields.Selection([
        ('annual', 'Annuelle'),
        ('semi_annual', 'Semestrielle'),
        ('quarterly', 'Trimestrielle')
    ], string='Fréquence d\'évaluation par défaut',
       default='annual',
       config_parameter='hr_objectives_evaluation.evaluation_frequency')
    
    auto_send_reminders = fields.Boolean(
        string='Envoi automatique de rappels',
        config_parameter='hr_objectives_evaluation.auto_send_reminders'
    )
    
    reminder_days_before = fields.Integer(
        string='Rappel X jours avant échéance',
        default=7,
        config_parameter='hr_objectives_evaluation.reminder_days_before'
    )
    
    # Configuration des notifications
    notify_manager_new_objective = fields.Boolean(
        string='Notifier le manager des nouveaux objectifs',
        default=True,
        config_parameter='hr_objectives_evaluation.notify_manager_new_objective'
    )
    
    notify_employee_evaluation = fields.Boolean(
        string='Notifier l\'employé des nouvelles évaluations',
        default=True,
        config_parameter='hr_objectives_evaluation.notify_employee_evaluation'
    )
    
    # Configuration des scores
    min_evaluation_score = fields.Float(
        string='Score minimum d\'évaluation',
        default=0.0,
        config_parameter='hr_objectives_evaluation.min_evaluation_score'
    )
    
    max_evaluation_score = fields.Float(
        string='Score maximum d\'évaluation',
        default=5.0,
        config_parameter='hr_objectives_evaluation.max_evaluation_score'
    )
