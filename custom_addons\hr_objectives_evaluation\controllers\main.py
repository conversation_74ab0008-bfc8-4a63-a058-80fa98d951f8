# -*- coding: utf-8 -*-

from odoo import http, _
from odoo.http import request
from odoo.addons.portal.controllers.portal import CustomerPortal
import json


class HrObjectivesController(http.Controller):

    @http.route('/hr_objectives/dashboard', type='http', auth='user', website=True)
    def objectives_dashboard(self, **kwargs):
        """Dashboard des objectifs pour l'utilisateur connecté"""
        user = request.env.user
        employee = request.env['hr.employee'].search([('user_id', '=', user.id)], limit=1)
        
        if not employee:
            return request.render('hr_objectives_evaluation.no_employee_error')
        
        # Récupération des données
        objectives = request.env['hr.objective'].search([
            ('employee_id', '=', employee.id)
        ])
        
        evaluations = request.env['hr.evaluation'].search([
            ('employee_id', '=', employee.id)
        ], limit=5, order='evaluation_date desc')
        
        # Statistiques
        stats = {
            'total_objectives': len(objectives),
            'active_objectives': len(objectives.filtered(lambda o: o.state == 'in_progress')),
            'achieved_objectives': len(objectives.filtered(lambda o: o.state == 'achieved')),
            'overdue_objectives': len(objectives.filtered(lambda o: o.is_overdue)),
            'avg_progress': sum(objectives.mapped('progress')) / len(objectives) if objectives else 0,
            'last_evaluation_score': evaluations[0].overall_score if evaluations else 0,
        }
        
        values = {
            'employee': employee,
            'objectives': objectives,
            'evaluations': evaluations,
            'stats': stats,
        }
        
        return request.render('hr_objectives_evaluation.dashboard_template', values)

    @http.route('/hr_objectives/api/update_progress', type='json', auth='user')
    def update_objective_progress(self, objective_id, progress):
        """API pour mettre à jour la progression d'un objectif"""
        try:
            objective = request.env['hr.objective'].browse(int(objective_id))
            
            # Vérification des droits
            user = request.env.user
            employee = request.env['hr.employee'].search([('user_id', '=', user.id)], limit=1)
            
            if not employee or objective.employee_id != employee:
                return {'error': 'Accès non autorisé'}
            
            # Mise à jour
            objective.write({'progress': float(progress)})
            
            # Mise à jour automatique du statut
            if progress >= 100 and objective.state == 'in_progress':
                objective.action_achieve()
            
            return {'success': True, 'message': 'Progression mise à jour'}
            
        except Exception as e:
            return {'error': str(e)}

    @http.route('/hr_objectives/api/objectives_data', type='json', auth='user')
    def get_objectives_data(self, **kwargs):
        """API pour récupérer les données des objectifs (pour graphiques)"""
        user = request.env.user
        employee = request.env['hr.employee'].search([('user_id', '=', user.id)], limit=1)
        
        if not employee:
            return {'error': 'Employé non trouvé'}
        
        objectives = request.env['hr.objective'].search([
            ('employee_id', '=', employee.id)
        ])
        
        # Données pour graphique de progression
        progress_data = []
        for obj in objectives:
            progress_data.append({
                'name': obj.name,
                'progress': obj.progress,
                'state': obj.state,
                'deadline': obj.deadline.strftime('%Y-%m-%d') if obj.deadline else None
            })
        
        # Données pour graphique d'état
        state_data = {}
        for state in ['draft', 'in_progress', 'achieved', 'partially_achieved', 'not_achieved', 'cancelled']:
            count = len(objectives.filtered(lambda o: o.state == state))
            if count > 0:
                state_data[state] = count
        
        return {
            'progress_data': progress_data,
            'state_data': state_data,
            'total_objectives': len(objectives)
        }


class HrEvaluationController(http.Controller):

    @http.route('/hr_evaluation/self_evaluation/<int:evaluation_id>', type='http', auth='user', website=True)
    def self_evaluation_form(self, evaluation_id, **kwargs):
        """Formulaire d'auto-évaluation"""
        evaluation = request.env['hr.evaluation'].browse(evaluation_id)
        
        # Vérification des droits
        user = request.env.user
        employee = request.env['hr.employee'].search([('user_id', '=', user.id)], limit=1)
        
        if not employee or evaluation.employee_id != employee:
            return request.render('hr_objectives_evaluation.access_denied')
        
        if evaluation.state != 'self_evaluation':
            return request.render('hr_objectives_evaluation.evaluation_not_available')
        
        values = {
            'evaluation': evaluation,
            'criteria_lines': evaluation.criteria_line_ids,
        }
        
        return request.render('hr_objectives_evaluation.self_evaluation_template', values)

    @http.route('/hr_evaluation/submit_self_evaluation', type='http', auth='user', website=True, methods=['POST'])
    def submit_self_evaluation(self, **post):
        """Soumission de l'auto-évaluation"""
        evaluation_id = int(post.get('evaluation_id'))
        evaluation = request.env['hr.evaluation'].browse(evaluation_id)
        
        # Vérification des droits
        user = request.env.user
        employee = request.env['hr.employee'].search([('user_id', '=', user.id)], limit=1)
        
        if not employee or evaluation.employee_id != employee:
            return request.redirect('/hr_objectives/dashboard')
        
        # Mise à jour des scores
        for key, value in post.items():
            if key.startswith('score_'):
                criteria_line_id = int(key.replace('score_', ''))
                criteria_line = evaluation.criteria_line_ids.filtered(
                    lambda l: l.id == criteria_line_id
                )
                if criteria_line:
                    criteria_line.write({'score': float(value)})
            
            elif key.startswith('comment_'):
                criteria_line_id = int(key.replace('comment_', ''))
                criteria_line = evaluation.criteria_line_ids.filtered(
                    lambda l: l.id == criteria_line_id
                )
                if criteria_line:
                    criteria_line.write({'comments': value})
        
        # Mise à jour des commentaires généraux
        evaluation.write({
            'employee_comments': post.get('employee_comments', ''),
            'self_evaluation_score': evaluation.overall_score,
        })
        
        # Passage à l'étape suivante
        evaluation.action_submit_self_evaluation()
        
        return request.redirect('/hr_objectives/dashboard?message=evaluation_submitted')


class HrObjectivesPortal(CustomerPortal):
    
    def _prepare_home_portal_values(self, counters):
        """Ajouter les compteurs d'objectifs et évaluations au portail"""
        values = super()._prepare_home_portal_values(counters)
        
        user = request.env.user
        employee = request.env['hr.employee'].search([('user_id', '=', user.id)], limit=1)
        
        if employee:
            if 'objective_count' in counters:
                values['objective_count'] = request.env['hr.objective'].search_count([
                    ('employee_id', '=', employee.id)
                ])
            
            if 'evaluation_count' in counters:
                values['evaluation_count'] = request.env['hr.evaluation'].search_count([
                    ('employee_id', '=', employee.id)
                ])
        
        return values

    @http.route(['/my/objectives', '/my/objectives/page/<int:page>'], type='http', auth="user", website=True)
    def portal_my_objectives(self, page=1, date_begin=None, date_end=None, sortby=None, **kw):
        """Page des objectifs dans le portail"""
        user = request.env.user
        employee = request.env['hr.employee'].search([('user_id', '=', user.id)], limit=1)
        
        if not employee:
            return request.render('hr_objectives_evaluation.no_employee_error')
        
        values = self._prepare_portal_layout_values()
        
        # Domaine de recherche
        domain = [('employee_id', '=', employee.id)]
        
        # Tri
        searchbar_sortings = {
            'date': {'label': _('Date limite'), 'order': 'deadline desc'},
            'name': {'label': _('Nom'), 'order': 'name'},
            'state': {'label': _('État'), 'order': 'state'},
            'progress': {'label': _('Progression'), 'order': 'progress desc'},
        }
        
        if not sortby:
            sortby = 'date'
        order = searchbar_sortings[sortby]['order']
        
        # Pagination
        objective_count = request.env['hr.objective'].search_count(domain)
        pager = request.website.pager(
            url="/my/objectives",
            url_args={'sortby': sortby},
            total=objective_count,
            page=page,
            step=self._items_per_page
        )
        
        # Récupération des objectifs
        objectives = request.env['hr.objective'].search(
            domain, order=order, limit=self._items_per_page, offset=pager['offset']
        )
        
        values.update({
            'objectives': objectives,
            'page_name': 'objective',
            'pager': pager,
            'default_url': '/my/objectives',
            'searchbar_sortings': searchbar_sortings,
            'sortby': sortby,
        })
        
        return request.render("hr_objectives_evaluation.portal_my_objectives", values)
