<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Extension de la vue Form des Employés -->
    <record id="view_hr_employee_form_objectives" model="ir.ui.view">
        <field name="name">hr.employee.form.objectives</field>
        <field name="model">hr.employee</field>
        <field name="inherit_id" ref="hr.view_employee_form"/>
        <field name="arch" type="xml">
            <xpath expr="//div[@name='button_box']" position="inside">
                <button name="action_view_objectives" type="object" class="oe_stat_button" icon="fa-bullseye">
                    <div class="o_field_widget o_stat_info">
                        <span class="o_stat_value">
                            <field name="active_objective_count"/>
                        </span>
                        <span class="o_stat_text">Objectifs Actifs</span>
                    </div>
                </button>
                <button name="action_view_evaluations" type="object" class="oe_stat_button" icon="fa-star">
                    <div class="o_field_widget o_stat_info">
                        <span class="o_stat_value">
                            <field name="evaluation_count"/>
                        </span>
                        <span class="o_stat_text">Évaluations</span>
                    </div>
                </button>
                <button name="action_create_evaluation" type="object" class="oe_stat_button" icon="fa-plus" 
                        groups="hr_objectives_evaluation.group_hr_objectives_manager">
                    <span class="o_stat_text">Nouvelle Évaluation</span>
                </button>
            </xpath>
            
            <xpath expr="//notebook" position="inside">
                <page string="Objectifs &amp; Évaluations" groups="hr_objectives_evaluation.group_hr_objectives_user">
                    <group>
                        <group string="Statistiques Objectifs">
                            <field name="active_objective_count" readonly="1"/>
                            <field name="completed_objective_count" readonly="1"/>
                        </group>
                        <group string="Statistiques Évaluations">
                            <field name="last_evaluation_date" readonly="1"/>
                            <field name="next_evaluation_date"/>
                            <field name="last_evaluation_score" readonly="1" widget="progressbar"/>
                            <field name="average_evaluation_score" readonly="1" widget="progressbar"/>
                            <field name="performance_trend" readonly="1" widget="badge"/>
                        </group>
                    </group>
                    
                    <separator string="Objectifs Récents"/>
                    <field name="objective_ids" mode="tree" limit="5">
                        <tree>
                            <field name="name"/>
                            <field name="deadline"/>
                            <field name="state" widget="badge"/>
                            <field name="progress" widget="progressbar"/>
                        </tree>
                    </field>
                    
                    <separator string="Évaluations Récentes"/>
                    <field name="evaluation_ids" mode="tree" limit="3">
                        <tree>
                            <field name="name"/>
                            <field name="evaluation_date"/>
                            <field name="evaluation_type"/>
                            <field name="state" widget="badge"/>
                            <field name="overall_score" widget="progressbar"/>
                        </tree>
                    </field>
                </page>
            </xpath>
        </field>
    </record>

    <!-- Vue Tree des Employés avec informations d'évaluation -->
    <record id="view_hr_employee_tree_objectives" model="ir.ui.view">
        <field name="name">hr.employee.tree.objectives</field>
        <field name="model">hr.employee</field>
        <field name="inherit_id" ref="hr.view_employee_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='department_id']" position="after">
                <field name="active_objective_count" string="Objectifs"/>
                <field name="last_evaluation_score" widget="progressbar" string="Dernière Éval."/>
                <field name="performance_trend" widget="badge" string="Tendance"/>
            </xpath>
        </field>
    </record>

    <!-- Action spécialisée pour les employés avec objectifs -->
    <record id="action_hr_employee_objectives" model="ir.actions.act_window">
        <field name="name">Employés - Objectifs &amp; Évaluations</field>
        <field name="res_model">hr.employee</field>
        <field name="view_mode">tree,form</field>
        <field name="view_id" ref="view_hr_employee_tree_objectives"/>
        <field name="context">{'search_default_my_team': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Aucun employé trouvé
            </p>
            <p>
                Gérez les objectifs et évaluations de vos employés.
            </p>
        </field>
    </record>
</odoo>
