<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Vue Tree des Modèles d'Évaluation -->
    <record id="view_hr_evaluation_template_tree" model="ir.ui.view">
        <field name="name">hr.evaluation.template.tree</field>
        <field name="model">hr.evaluation.template</field>
        <field name="arch" type="xml">
            <tree>
                <field name="name"/>
                <field name="template_type"/>
                <field name="frequency"/>
                <field name="evaluation_count"/>
                <field name="allow_self_evaluation"/>
                <field name="require_manager_approval"/>
                <field name="active"/>
            </tree>
        </field>
    </record>

    <!-- Vue Form des Modèles d'Évaluation -->
    <record id="view_hr_evaluation_template_form" model="ir.ui.view">
        <field name="name">hr.evaluation.template.form</field>
        <field name="model">hr.evaluation.template</field>
        <field name="arch" type="xml">
            <form>
                <header>
                    <button name="action_view_evaluations" string="Voir les Évaluations" type="object" class="btn-primary"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_evaluations" type="object" class="oe_stat_button" icon="fa-star">
                            <field name="evaluation_count" widget="statinfo" string="Évaluations"/>
                        </button>
                    </div>
                    
                    <widget name="web_ribbon" title="Archivé" bg_color="bg-danger" attrs="{'invisible': [('active', '=', True)]}"/>
                    
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Nom du modèle"/>
                        </h1>
                    </div>
                    
                    <group>
                        <group>
                            <field name="template_type"/>
                            <field name="frequency"/>
                            <field name="active"/>
                        </group>
                        <group>
                            <field name="allow_self_evaluation"/>
                            <field name="require_manager_approval"/>
                            <field name="require_hr_review"/>
                        </group>
                    </group>
                    
                    <group>
                        <field name="description" placeholder="Description du modèle"/>
                    </group>
                    
                    <notebook>
                        <page string="Critères d'Évaluation">
                            <field name="criteria_ids">
                                <tree>
                                    <field name="name"/>
                                    <field name="category_id"/>
                                    <field name="criteria_type"/>
                                    <field name="default_weight"/>
                                </tree>
                            </field>
                        </page>
                        
                        <page string="Applicabilité">
                            <group>
                                <group>
                                    <field name="department_ids" widget="many2many_tags"/>
                                </group>
                                <group>
                                    <field name="job_ids" widget="many2many_tags"/>
                                </group>
                            </group>
                            <div class="alert alert-info" role="alert">
                                <strong>Note:</strong> Si aucun département ou poste n'est spécifié, 
                                le modèle sera applicable à tous les employés.
                            </div>
                        </page>
                        
                        <page string="Instructions">
                            <field name="instructions" widget="html"/>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Vue Kanban des Modèles d'Évaluation -->
    <record id="view_hr_evaluation_template_kanban" model="ir.ui.view">
        <field name="name">hr.evaluation.template.kanban</field>
        <field name="model">hr.evaluation.template</field>
        <field name="arch" type="xml">
            <kanban class="o_kanban_mobile">
                <field name="name"/>
                <field name="template_type"/>
                <field name="evaluation_count"/>
                <field name="active"/>
                <templates>
                    <t t-name="kanban-box">
                        <div class="oe_kanban_card oe_kanban_global_click">
                            <div class="o_kanban_image">
                                <i class="fa fa-file-text-o fa-2x"/>
                            </div>
                            <div class="oe_kanban_details">
                                <div class="o_kanban_record_top">
                                    <div class="o_kanban_record_headings">
                                        <strong class="o_kanban_record_title">
                                            <field name="name"/>
                                        </strong>
                                    </div>
                                </div>
                                <div class="o_kanban_record_body">
                                    <field name="template_type" widget="badge"/>
                                    <br/>
                                    <t t-esc="record.evaluation_count.value"/> évaluations
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <!-- Vue Search des Modèles d'Évaluation -->
    <record id="view_hr_evaluation_template_search" model="ir.ui.view">
        <field name="name">hr.evaluation.template.search</field>
        <field name="model">hr.evaluation.template</field>
        <field name="arch" type="xml">
            <search>
                <field name="name"/>
                <field name="template_type"/>
                <field name="frequency"/>
                <separator/>
                <filter string="Actifs" name="active" domain="[('active', '=', True)]"/>
                <filter string="Archivés" name="inactive" domain="[('active', '=', False)]"/>
                <separator/>
                <filter string="Auto-évaluation" name="self_evaluation" domain="[('allow_self_evaluation', '=', True)]"/>
                <filter string="Approbation manager" name="manager_approval" domain="[('require_manager_approval', '=', True)]"/>
                <separator/>
                <group expand="0" string="Grouper par">
                    <filter string="Type" name="group_type" context="{'group_by': 'template_type'}"/>
                    <filter string="Fréquence" name="group_frequency" context="{'group_by': 'frequency'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Action pour les Modèles d'Évaluation -->
    <record id="action_hr_evaluation_template" model="ir.actions.act_window">
        <field name="name">Modèles d'Évaluation</field>
        <field name="res_model">hr.evaluation.template</field>
        <field name="view_mode">kanban,tree,form</field>
        <field name="search_view_id" ref="view_hr_evaluation_template_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Créer un nouveau modèle d'évaluation
            </p>
            <p>
                Définissez des modèles d'évaluation réutilisables avec des critères prédéfinis.
            </p>
        </field>
    </record>
</odoo>
