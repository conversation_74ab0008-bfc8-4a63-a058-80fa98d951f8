<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Menu principal -->
    <menuitem id="menu_hr_objectives_main" 
              name="Objectifs &amp; Évaluations" 
              parent="hr.menu_hr_root" 
              sequence="30"
              groups="group_hr_objectives_user"/>

    <!-- Sous-menus Objectifs -->
    <menuitem id="menu_hr_objectives" 
              name="Objectifs" 
              parent="menu_hr_objectives_main" 
              sequence="10"/>

    <menuitem id="menu_hr_objectives_all" 
              name="Tous les objectifs" 
              parent="menu_hr_objectives" 
              action="action_hr_objective" 
              sequence="10"/>

    <menuitem id="menu_hr_objectives_my" 
              name="Mes objectifs" 
              parent="menu_hr_objectives" 
              action="action_hr_objective_my" 
              sequence="20"/>

    <menuitem id="menu_hr_objectives_team" 
              name="Objectifs de l'équipe" 
              parent="menu_hr_objectives" 
              action="action_hr_objective_team" 
              sequence="30"
              groups="group_hr_objectives_manager"/>

    <!-- Sous-menus Évaluations -->
    <menuitem id="menu_hr_evaluations" 
              name="Évaluations" 
              parent="menu_hr_objectives_main" 
              sequence="20"/>

    <menuitem id="menu_hr_evaluations_all" 
              name="Toutes les évaluations" 
              parent="menu_hr_evaluations" 
              action="action_hr_evaluation" 
              sequence="10"/>

    <menuitem id="menu_hr_evaluations_my" 
              name="Mes évaluations" 
              parent="menu_hr_evaluations" 
              action="action_hr_evaluation_my" 
              sequence="20"/>

    <menuitem id="menu_hr_evaluations_to_review" 
              name="À réviser" 
              parent="menu_hr_evaluations" 
              action="action_hr_evaluation_to_review" 
              sequence="30"
              groups="group_hr_objectives_manager"/>

    <!-- Sous-menus Configuration -->
    <menuitem id="menu_hr_objectives_config" 
              name="Configuration" 
              parent="menu_hr_objectives_main" 
              sequence="90"
              groups="group_hr_objectives_manager"/>

    <menuitem id="menu_hr_evaluation_templates" 
              name="Modèles d'évaluation" 
              parent="menu_hr_objectives_config" 
              action="action_hr_evaluation_template" 
              sequence="10"/>

    <menuitem id="menu_hr_evaluation_criteria" 
              name="Critères d'évaluation" 
              parent="menu_hr_objectives_config" 
              action="action_hr_evaluation_criteria" 
              sequence="20"/>

    <menuitem id="menu_hr_evaluation_criteria_categories" 
              name="Catégories de critères" 
              parent="menu_hr_objectives_config" 
              action="action_hr_evaluation_criteria_category" 
              sequence="30"/>

    <!-- Sous-menus Assistants -->
    <menuitem id="menu_hr_objectives_wizards"
              name="Assistants"
              parent="menu_hr_objectives_main"
              sequence="70"
              groups="group_hr_objectives_manager"/>

    <menuitem id="menu_hr_evaluation_wizard"
              name="Créer des Évaluations"
              parent="menu_hr_objectives_wizards"
              action="action_hr_evaluation_wizard"
              sequence="10"/>

    <menuitem id="menu_hr_objective_bulk_wizard"
              name="Créer des Objectifs en Masse"
              parent="menu_hr_objectives_wizards"
              action="action_hr_objective_bulk_wizard"
              sequence="20"/>

    <menuitem id="menu_hr_objective_report_wizard"
              name="Rapport d'Objectifs"
              parent="menu_hr_objectives_wizards"
              action="action_hr_objective_report_wizard"
              sequence="30"/>

    <!-- Sous-menus Rapports -->
    <menuitem id="menu_hr_objectives_reports"
              name="Rapports"
              parent="menu_hr_objectives_main"
              sequence="80"
              groups="group_hr_objectives_manager"/>

    <!-- Actions spécifiques -->
    <record id="action_hr_objective_my" model="ir.actions.act_window">
        <field name="name">Mes Objectifs</field>
        <field name="res_model">hr.objective</field>
        <field name="view_mode">kanban,tree,form</field>
        <field name="domain">[('employee_id.user_id', '=', uid)]</field>
        <field name="context">{'default_employee_id': False}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Créer votre premier objectif
            </p>
            <p>
                Définissez vos objectifs personnels et suivez votre progression.
            </p>
        </field>
    </record>

    <record id="action_hr_objective_team" model="ir.actions.act_window">
        <field name="name">Objectifs de l'Équipe</field>
        <field name="res_model">hr.objective</field>
        <field name="view_mode">kanban,tree,form</field>
        <field name="domain">[('manager_id.user_id', '=', uid)]</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Aucun objectif d'équipe
            </p>
            <p>
                Créez des objectifs pour votre équipe et suivez leur progression.
            </p>
        </field>
    </record>

    <record id="action_hr_evaluation_my" model="ir.actions.act_window">
        <field name="name">Mes Évaluations</field>
        <field name="res_model">hr.evaluation</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('employee_id.user_id', '=', uid)]</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Aucune évaluation
            </p>
            <p>
                Vos évaluations apparaîtront ici.
            </p>
        </field>
    </record>

    <record id="action_hr_evaluation_to_review" model="ir.actions.act_window">
        <field name="name">Évaluations à Réviser</field>
        <field name="res_model">hr.evaluation</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('evaluator_id.user_id', '=', uid), ('state', 'in', ['manager_evaluation', 'hr_review'])]</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Aucune évaluation à réviser
            </p>
            <p>
                Les évaluations en attente de votre révision apparaîtront ici.
            </p>
        </field>
    </record>
</odoo>
