# -*- coding: utf-8 -*-

from odoo import models, fields, api, _


class HrEmployee(models.Model):
    _inherit = 'hr.employee'

    # Objectifs
    objective_ids = fields.One2many(
        'hr.objective',
        'employee_id',
        string='Objectifs'
    )
    active_objective_count = fields.Integer(
        string='Objectifs actifs',
        compute='_compute_objective_counts'
    )
    completed_objective_count = fields.Integer(
        string='Objectifs terminés',
        compute='_compute_objective_counts'
    )
    
    # Évaluations
    evaluation_ids = fields.One2many(
        'hr.evaluation',
        'employee_id',
        string='Évaluations'
    )
    evaluation_count = fields.Integer(
        string='Nombre d\'évaluations',
        compute='_compute_evaluation_count'
    )
    last_evaluation_date = fields.Date(
        string='Dernière évaluation',
        compute='_compute_last_evaluation'
    )
    next_evaluation_date = fields.Date(
        string='Prochaine évaluation',
        help="Date prévue pour la prochaine évaluation"
    )
    
    # Scores moyens
    average_evaluation_score = fields.Float(
        string='Score moyen d\'évaluation',
        compute='_compute_average_scores'
    )
    last_evaluation_score = fields.Float(
        string='Dernier score d\'évaluation',
        compute='_compute_last_evaluation'
    )
    
    # Performance
    performance_trend = fields.Selection([
        ('improving', 'En amélioration'),
        ('stable', 'Stable'),
        ('declining', 'En déclin'),
        ('new', 'Nouveau')
    ], string='Tendance de performance', compute='_compute_performance_trend')
    
    @api.depends('objective_ids.state')
    def _compute_objective_counts(self):
        for employee in self:
            objectives = employee.objective_ids
            employee.active_objective_count = len(objectives.filtered(
                lambda o: o.state in ['draft', 'in_progress']
            ))
            employee.completed_objective_count = len(objectives.filtered(
                lambda o: o.state in ['achieved', 'partially_achieved']
            ))
    
    @api.depends('evaluation_ids')
    def _compute_evaluation_count(self):
        for employee in self:
            employee.evaluation_count = len(employee.evaluation_ids)
    
    @api.depends('evaluation_ids.evaluation_date', 'evaluation_ids.overall_score')
    def _compute_last_evaluation(self):
        for employee in self:
            evaluations = employee.evaluation_ids.filtered(
                lambda e: e.state == 'completed'
            ).sorted('evaluation_date', reverse=True)
            
            if evaluations:
                last_eval = evaluations[0]
                employee.last_evaluation_date = last_eval.evaluation_date
                employee.last_evaluation_score = last_eval.overall_score
            else:
                employee.last_evaluation_date = False
                employee.last_evaluation_score = 0.0
    
    @api.depends('evaluation_ids.overall_score')
    def _compute_average_scores(self):
        for employee in self:
            completed_evaluations = employee.evaluation_ids.filtered(
                lambda e: e.state == 'completed' and e.overall_score > 0
            )
            if completed_evaluations:
                employee.average_evaluation_score = sum(
                    completed_evaluations.mapped('overall_score')
                ) / len(completed_evaluations)
            else:
                employee.average_evaluation_score = 0.0
    
    @api.depends('evaluation_ids.overall_score', 'evaluation_ids.evaluation_date')
    def _compute_performance_trend(self):
        for employee in self:
            evaluations = employee.evaluation_ids.filtered(
                lambda e: e.state == 'completed' and e.overall_score > 0
            ).sorted('evaluation_date', reverse=True)
            
            if len(evaluations) < 2:
                employee.performance_trend = 'new'
            else:
                recent_score = evaluations[0].overall_score
                previous_score = evaluations[1].overall_score
                
                if recent_score > previous_score + 0.2:
                    employee.performance_trend = 'improving'
                elif recent_score < previous_score - 0.2:
                    employee.performance_trend = 'declining'
                else:
                    employee.performance_trend = 'stable'
    
    def action_view_objectives(self):
        """Action pour voir les objectifs de l'employé"""
        return {
            'name': _('Objectifs de %s') % self.name,
            'type': 'ir.actions.act_window',
            'res_model': 'hr.objective',
            'view_mode': 'tree,form,kanban',
            'domain': [('employee_id', '=', self.id)],
            'context': {'default_employee_id': self.id}
        }
    
    def action_view_evaluations(self):
        """Action pour voir les évaluations de l'employé"""
        return {
            'name': _('Évaluations de %s') % self.name,
            'type': 'ir.actions.act_window',
            'res_model': 'hr.evaluation',
            'view_mode': 'tree,form',
            'domain': [('employee_id', '=', self.id)],
            'context': {'default_employee_id': self.id}
        }
    
    def action_create_evaluation(self):
        """Action pour créer une nouvelle évaluation"""
        return {
            'name': _('Nouvelle évaluation pour %s') % self.name,
            'type': 'ir.actions.act_window',
            'res_model': 'hr.evaluation',
            'view_mode': 'form',
            'context': {
                'default_employee_id': self.id,
                'default_evaluator_id': self.parent_id.id if self.parent_id else False,
            },
            'target': 'new'
        }
