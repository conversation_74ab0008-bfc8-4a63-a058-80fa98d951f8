<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Groupes de sécurité -->
    <record id="group_hr_objectives_user" model="res.groups">
        <field name="name">Utilisateur Objectifs RH</field>
        <field name="category_id" ref="base.module_category_human_resources"/>
        <field name="implied_ids" eval="[(4, ref('base.group_user'))]"/>
    </record>

    <record id="group_hr_objectives_manager" model="res.groups">
        <field name="name">Manager Objectifs RH</field>
        <field name="category_id" ref="base.module_category_human_resources"/>
        <field name="implied_ids" eval="[(4, ref('group_hr_objectives_user'))]"/>
    </record>

    <record id="group_hr_objectives_admin" model="res.groups">
        <field name="name">Administrateur Objectifs RH</field>
        <field name="category_id" ref="base.module_category_human_resources"/>
        <field name="implied_ids" eval="[(4, ref('group_hr_objectives_manager')), (4, ref('hr.group_hr_manager'))]"/>
    </record>

    <!-- Règles de sécurité pour les Objectifs -->
    <record id="hr_objective_rule_employee" model="ir.rule">
        <field name="name">Objectifs: Employé peut voir ses propres objectifs</field>
        <field name="model_id" ref="model_hr_objective"/>
        <field name="domain_force">[('employee_id.user_id', '=', user.id)]</field>
        <field name="groups" eval="[(4, ref('group_hr_objectives_user'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="False"/>
        <field name="perm_unlink" eval="False"/>
    </record>

    <record id="hr_objective_rule_manager" model="ir.rule">
        <field name="name">Objectifs: Manager peut voir les objectifs de son équipe</field>
        <field name="model_id" ref="model_hr_objective"/>
        <field name="domain_force">['|', ('employee_id.parent_id.user_id', '=', user.id), ('employee_id.user_id', '=', user.id)]</field>
        <field name="groups" eval="[(4, ref('group_hr_objectives_manager'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="True"/>
    </record>

    <record id="hr_objective_rule_admin" model="ir.rule">
        <field name="name">Objectifs: Admin peut tout voir</field>
        <field name="model_id" ref="model_hr_objective"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('group_hr_objectives_admin'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="True"/>
    </record>

    <!-- Règles de sécurité pour les Évaluations -->
    <record id="hr_evaluation_rule_employee" model="ir.rule">
        <field name="name">Évaluations: Employé peut voir ses propres évaluations</field>
        <field name="model_id" ref="model_hr_evaluation"/>
        <field name="domain_force">[('employee_id.user_id', '=', user.id)]</field>
        <field name="groups" eval="[(4, ref('group_hr_objectives_user'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="False"/>
        <field name="perm_unlink" eval="False"/>
    </record>

    <record id="hr_evaluation_rule_manager" model="ir.rule">
        <field name="name">Évaluations: Manager peut voir les évaluations de son équipe</field>
        <field name="model_id" ref="model_hr_evaluation"/>
        <field name="domain_force">['|', ('employee_id.parent_id.user_id', '=', user.id), ('evaluator_id.user_id', '=', user.id)]</field>
        <field name="groups" eval="[(4, ref('group_hr_objectives_manager'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="True"/>
    </record>

    <record id="hr_evaluation_rule_admin" model="ir.rule">
        <field name="name">Évaluations: Admin peut tout voir</field>
        <field name="model_id" ref="model_hr_evaluation"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('group_hr_objectives_admin'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="True"/>
    </record>

    <!-- Règles pour les Critères d'évaluation -->
    <record id="hr_evaluation_criteria_rule_all" model="ir.rule">
        <field name="name">Critères: Tous peuvent lire</field>
        <field name="model_id" ref="model_hr_evaluation_criteria"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('group_hr_objectives_user'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="False"/>
        <field name="perm_create" eval="False"/>
        <field name="perm_unlink" eval="False"/>
    </record>

    <record id="hr_evaluation_criteria_rule_admin" model="ir.rule">
        <field name="name">Critères: Admin peut tout faire</field>
        <field name="model_id" ref="model_hr_evaluation_criteria"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('group_hr_objectives_admin'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="True"/>
    </record>

    <!-- Règles pour les Templates d'évaluation -->
    <record id="hr_evaluation_template_rule_all" model="ir.rule">
        <field name="name">Templates: Tous peuvent lire</field>
        <field name="model_id" ref="model_hr_evaluation_template"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('group_hr_objectives_user'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="False"/>
        <field name="perm_create" eval="False"/>
        <field name="perm_unlink" eval="False"/>
    </record>

    <record id="hr_evaluation_template_rule_manager" model="ir.rule">
        <field name="name">Templates: Manager peut modifier</field>
        <field name="model_id" ref="model_hr_evaluation_template"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('group_hr_objectives_manager'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="False"/>
    </record>

    <record id="hr_evaluation_template_rule_admin" model="ir.rule">
        <field name="name">Templates: Admin peut tout faire</field>
        <field name="model_id" ref="model_hr_evaluation_template"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('group_hr_objectives_admin'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="True"/>
    </record>
</odoo>
