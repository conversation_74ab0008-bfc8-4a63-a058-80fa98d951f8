<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Catégories de critères d'évaluation -->
    <record id="criteria_category_competency" model="hr.evaluation.criteria.category">
        <field name="name">Compétences</field>
        <field name="description">Compétences techniques et professionnelles</field>
        <field name="sequence">10</field>
        <field name="color">1</field>
    </record>

    <record id="criteria_category_behavior" model="hr.evaluation.criteria.category">
        <field name="name">Comportement</field>
        <field name="description">Comportements et attitudes au travail</field>
        <field name="sequence">20</field>
        <field name="color">2</field>
    </record>

    <record id="criteria_category_performance" model="hr.evaluation.criteria.category">
        <field name="name">Performance</field>
        <field name="description">Résultats et performance</field>
        <field name="sequence">30</field>
        <field name="color">3</field>
    </record>

    <!-- Critères d'évaluation par défaut -->
    <record id="criteria_technical_skills" model="hr.evaluation.criteria">
        <field name="name">Compétences techniques</field>
        <field name="description">Maîtrise des compétences techniques requises pour le poste</field>
        <field name="category_id" ref="criteria_category_competency"/>
        <field name="criteria_type">skill</field>
        <field name="default_weight">1.0</field>
        <field name="sequence">10</field>
        <field name="level_1_description">Compétences insuffisantes, nécessite une formation importante</field>
        <field name="level_2_description">Compétences en développement, nécessite un accompagnement</field>
        <field name="level_3_description">Compétences satisfaisantes pour le poste</field>
        <field name="level_4_description">Bonnes compétences, peut aider les autres</field>
        <field name="level_5_description">Expert dans le domaine, référent technique</field>
    </record>

    <record id="criteria_communication" model="hr.evaluation.criteria">
        <field name="name">Communication</field>
        <field name="description">Capacité à communiquer efficacement</field>
        <field name="category_id" ref="criteria_category_behavior"/>
        <field name="criteria_type">communication</field>
        <field name="default_weight">1.0</field>
        <field name="sequence">20</field>
        <field name="level_1_description">Communication difficile, malentendus fréquents</field>
        <field name="level_2_description">Communication basique, peut s'améliorer</field>
        <field name="level_3_description">Communication claire et appropriée</field>
        <field name="level_4_description">Excellente communication, facilite les échanges</field>
        <field name="level_5_description">Communication exceptionnelle, inspire et motive</field>
    </record>

    <record id="criteria_teamwork" model="hr.evaluation.criteria">
        <field name="name">Travail d'équipe</field>
        <field name="description">Capacité à travailler en équipe</field>
        <field name="category_id" ref="criteria_category_behavior"/>
        <field name="criteria_type">teamwork</field>
        <field name="default_weight">1.0</field>
        <field name="sequence">30</field>
        <field name="level_1_description">Difficultés à travailler en équipe</field>
        <field name="level_2_description">Collaboration limitée</field>
        <field name="level_3_description">Bon esprit d'équipe</field>
        <field name="level_4_description">Excellent collaborateur</field>
        <field name="level_5_description">Leader naturel, fédère l'équipe</field>
    </record>

    <record id="criteria_quality" model="hr.evaluation.criteria">
        <field name="name">Qualité du travail</field>
        <field name="description">Qualité et précision du travail fourni</field>
        <field name="category_id" ref="criteria_category_performance"/>
        <field name="criteria_type">quality</field>
        <field name="default_weight">1.5</field>
        <field name="sequence">40</field>
        <field name="level_1_description">Qualité insuffisante, erreurs fréquentes</field>
        <field name="level_2_description">Qualité variable, nécessite des corrections</field>
        <field name="level_3_description">Qualité satisfaisante</field>
        <field name="level_4_description">Haute qualité, peu d'erreurs</field>
        <field name="level_5_description">Qualité exceptionnelle, travail exemplaire</field>
    </record>

    <record id="criteria_productivity" model="hr.evaluation.criteria">
        <field name="name">Productivité</field>
        <field name="description">Capacité à atteindre les objectifs dans les délais</field>
        <field name="category_id" ref="criteria_category_performance"/>
        <field name="criteria_type">productivity</field>
        <field name="default_weight">1.5</field>
        <field name="sequence">50</field>
        <field name="level_1_description">Productivité insuffisante, objectifs non atteints</field>
        <field name="level_2_description">Productivité en dessous des attentes</field>
        <field name="level_3_description">Productivité satisfaisante</field>
        <field name="level_4_description">Haute productivité, dépasse les objectifs</field>
        <field name="level_5_description">Productivité exceptionnelle, résultats remarquables</field>
    </record>

    <record id="criteria_innovation" model="hr.evaluation.criteria">
        <field name="name">Innovation et créativité</field>
        <field name="description">Capacité à proposer des solutions innovantes</field>
        <field name="category_id" ref="criteria_category_competency"/>
        <field name="criteria_type">innovation</field>
        <field name="default_weight">0.8</field>
        <field name="sequence">60</field>
        <field name="level_1_description">Peu d'initiatives, suit les procédures</field>
        <field name="level_2_description">Quelques suggestions d'amélioration</field>
        <field name="level_3_description">Propose régulièrement des améliorations</field>
        <field name="level_4_description">Très créatif, solutions innovantes</field>
        <field name="level_5_description">Visionnaire, transforme les processus</field>
    </record>

    <!-- Modèles d'évaluation par défaut -->
    <record id="template_annual_evaluation" model="hr.evaluation.template">
        <field name="name">Évaluation Annuelle Standard</field>
        <field name="description">Modèle d'évaluation annuelle pour tous les employés</field>
        <field name="template_type">annual</field>
        <field name="allow_self_evaluation">True</field>
        <field name="require_manager_approval">True</field>
        <field name="require_hr_review">False</field>
        <field name="frequency">annual</field>
        <field name="instructions"><![CDATA[
            <h3>Instructions pour l'évaluation annuelle</h3>
            <p>Cette évaluation couvre la période du 1er janvier au 31 décembre.</p>
            <h4>Pour l'employé :</h4>
            <ul>
                <li>Complétez votre auto-évaluation en toute honnêteté</li>
                <li>Fournissez des exemples concrets pour chaque critère</li>
                <li>Identifiez vos points forts et axes d'amélioration</li>
            </ul>
            <h4>Pour le manager :</h4>
            <ul>
                <li>Basez-vous sur des faits observables</li>
                <li>Donnez des exemples spécifiques</li>
                <li>Proposez un plan de développement</li>
            </ul>
        ]]></field>
        <field name="criteria_ids" eval="[(6, 0, [
            ref('criteria_technical_skills'),
            ref('criteria_communication'),
            ref('criteria_teamwork'),
            ref('criteria_quality'),
            ref('criteria_productivity'),
            ref('criteria_innovation')
        ])]"/>
    </record>

    <record id="template_probation_evaluation" model="hr.evaluation.template">
        <field name="name">Évaluation Période d'Essai</field>
        <field name="description">Modèle d'évaluation pour la fin de période d'essai</field>
        <field name="template_type">probation</field>
        <field name="allow_self_evaluation">True</field>
        <field name="require_manager_approval">True</field>
        <field name="require_hr_review">True</field>
        <field name="frequency">on_demand</field>
        <field name="instructions"><![CDATA[
            <h3>Évaluation de fin de période d'essai</h3>
            <p>Cette évaluation détermine la confirmation ou non de l'employé.</p>
            <p><strong>Critères de validation :</strong></p>
            <ul>
                <li>Adaptation au poste et à l'équipe</li>
                <li>Acquisition des compétences de base</li>
                <li>Respect des valeurs de l'entreprise</li>
            </ul>
        ]]></field>
        <field name="criteria_ids" eval="[(6, 0, [
            ref('criteria_technical_skills'),
            ref('criteria_communication'),
            ref('criteria_teamwork'),
            ref('criteria_quality')
        ])]"/>
    </record>

    <!-- Configuration par défaut -->
    <record id="ir_config_parameter_default_objective_duration" model="ir.config_parameter">
        <field name="key">hr_objectives_evaluation.default_objective_duration</field>
        <field name="value">365</field>
    </record>

    <record id="ir_config_parameter_evaluation_frequency" model="ir.config_parameter">
        <field name="key">hr_objectives_evaluation.evaluation_frequency</field>
        <field name="value">annual</field>
    </record>

    <record id="ir_config_parameter_auto_send_reminders" model="ir.config_parameter">
        <field name="key">hr_objectives_evaluation.auto_send_reminders</field>
        <field name="value">True</field>
    </record>

    <record id="ir_config_parameter_reminder_days_before" model="ir.config_parameter">
        <field name="key">hr_objectives_evaluation.reminder_days_before</field>
        <field name="value">7</field>
    </record>
</odoo>
