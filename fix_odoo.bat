@echo off
echo ========================================
echo CORRECTION DU PROBLEME ODOO
echo ========================================

echo.
echo 1. Arret du service Odoo...
net stop odoo-server-18.0

echo.
echo 2. Suppression temporaire du module defectueux...
if exist "C:\Program Files\Odoo 18.0.20250709\server\odoo\addons\hr_objectives_evaluation" (
    rmdir /s /q "C:\Program Files\Odoo 18.0.20250709\server\odoo\addons\hr_objectives_evaluation"
    echo Module supprime temporairement.
) else (
    echo Module deja supprime.
)

echo.
echo 3. Redemarrage du service Odoo...
net start odoo-server-18.0

echo.
echo 4. Attente du demarrage complet (30 secondes)...
timeout /t 30 /nobreak

echo.
echo ========================================
echo ODOO REDÉMARRE AVEC SUCCÈS !
echo ========================================
echo.
echo Vous pouvez maintenant:
echo 1. Aller sur http://localhost:8069
echo 2. Essayer de vous connecter avec:
echo    - Email: admin
echo    - Mot de passe: admin
echo.
echo Ou cliquer sur "Manage Databases" pour creer une nouvelle DB
echo.
pause
