# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
from datetime import datetime


class HrEvaluation(models.Model):
    _name = 'hr.evaluation'
    _description = 'Évaluation RH'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'evaluation_date desc'

    name = fields.Char(
        string='Nom de l\'évaluation',
        required=True,
        tracking=True
    )
    employee_id = fields.Many2one(
        'hr.employee',
        string='Employé évalué',
        required=True,
        tracking=True
    )
    evaluator_id = fields.Many2one(
        'hr.employee',
        string='Évaluateur',
        required=True,
        tracking=True
    )
    manager_id = fields.Many2one(
        'hr.employee',
        string='Manager',
        related='employee_id.parent_id',
        store=True
    )
    
    # Dates
    evaluation_date = fields.Date(
        string='Date d\'évaluation',
        required=True,
        default=fields.Date.today,
        tracking=True
    )
    period_start = fields.Date(
        string='Début de période',
        required=True
    )
    period_end = fields.Date(
        string='Fin de période',
        required=True
    )
    
    # Type d'évaluation
    evaluation_type = fields.Selection([
        ('annual', 'Annuelle'),
        ('mid_year', 'Mi-année'),
        ('quarterly', 'Trimestrielle'),
        ('probation', 'Période d\'essai'),
        ('project', 'Fin de projet'),
        ('360', 'Évaluation 360°')
    ], string='Type d\'évaluation', required=True, default='annual')
    
    # État
    state = fields.Selection([
        ('draft', 'Brouillon'),
        ('self_evaluation', 'Auto-évaluation'),
        ('manager_evaluation', 'Évaluation manager'),
        ('hr_review', 'Révision RH'),
        ('completed', 'Terminée'),
        ('cancelled', 'Annulée')
    ], string='État', default='draft', tracking=True)
    
    # Template et critères
    template_id = fields.Many2one(
        'hr.evaluation.template',
        string='Modèle d\'évaluation'
    )
    criteria_line_ids = fields.One2many(
        'hr.evaluation.criteria.line',
        'evaluation_id',
        string='Critères d\'évaluation'
    )
    
    # Objectifs liés
    objective_id = fields.Many2one(
        'hr.objective',
        string='Objectif principal'
    )
    objective_ids = fields.Many2many(
        'hr.objective',
        string='Objectifs évalués'
    )
    
    # Scores et commentaires
    overall_score = fields.Float(
        string='Score global',
        compute='_compute_overall_score',
        store=True
    )
    self_evaluation_score = fields.Float(string='Score auto-évaluation')
    manager_score = fields.Float(string='Score manager')
    
    # Commentaires
    employee_comments = fields.Html(string='Commentaires employé')
    manager_comments = fields.Html(string='Commentaires manager')
    hr_comments = fields.Html(string='Commentaires RH')
    
    # Points forts et axes d'amélioration
    strengths = fields.Html(string='Points forts')
    improvement_areas = fields.Html(string='Axes d\'amélioration')
    development_plan = fields.Html(string='Plan de développement')
    
    # Recommandations
    recommendation = fields.Selection([
        ('exceed', 'Dépasse les attentes'),
        ('meet', 'Répond aux attentes'),
        ('below', 'En dessous des attentes'),
        ('unsatisfactory', 'Insatisfaisant')
    ], string='Recommandation')
    
    # Signature et validation
    employee_signature = fields.Boolean(string='Signature employé')
    manager_signature = fields.Boolean(string='Signature manager')
    hr_signature = fields.Boolean(string='Signature RH')
    
    @api.depends('criteria_line_ids.score')
    def _compute_overall_score(self):
        for evaluation in self:
            if evaluation.criteria_line_ids:
                total_score = sum(line.score * line.weight for line in evaluation.criteria_line_ids)
                total_weight = sum(line.weight for line in evaluation.criteria_line_ids)
                evaluation.overall_score = total_score / total_weight if total_weight else 0
            else:
                evaluation.overall_score = 0
    
    @api.constrains('period_start', 'period_end', 'evaluation_date')
    def _check_dates(self):
        for record in self:
            if record.period_start and record.period_end:
                if record.period_start > record.period_end:
                    raise ValidationError(_("La date de début de période ne peut pas être postérieure à la date de fin."))
            if record.evaluation_date and record.period_end:
                if record.evaluation_date < record.period_end:
                    raise ValidationError(_("La date d'évaluation ne peut pas être antérieure à la fin de période."))
    
    def action_start_self_evaluation(self):
        """Démarre l'auto-évaluation"""
        self.write({'state': 'self_evaluation'})
        self.message_post(body=_("L'auto-évaluation a été démarrée."))
    
    def action_submit_self_evaluation(self):
        """Soumet l'auto-évaluation"""
        self.write({'state': 'manager_evaluation'})
        self.message_post(body=_("L'auto-évaluation a été soumise."))
    
    def action_complete_manager_evaluation(self):
        """Termine l'évaluation manager"""
        self.write({'state': 'hr_review'})
        self.message_post(body=_("L'évaluation manager a été terminée."))
    
    def action_complete_evaluation(self):
        """Termine l'évaluation complète"""
        self.write({'state': 'completed'})
        self.message_post(body=_("L'évaluation a été terminée."))
    
    def action_cancel(self):
        """Annule l'évaluation"""
        self.write({'state': 'cancelled'})
        self.message_post(body=_("L'évaluation a été annulée."))
    
    @api.onchange('template_id')
    def _onchange_template_id(self):
        """Charge les critères du template"""
        if self.template_id:
            criteria_lines = []
            for criteria in self.template_id.criteria_ids:
                criteria_lines.append((0, 0, {
                    'criteria_id': criteria.id,
                    'weight': criteria.weight,
                    'score': 0,
                }))
            self.criteria_line_ids = criteria_lines


class HrEvaluationCriteriaLine(models.Model):
    _name = 'hr.evaluation.criteria.line'
    _description = 'Ligne de critère d\'évaluation'

    evaluation_id = fields.Many2one(
        'hr.evaluation',
        string='Évaluation',
        required=True,
        ondelete='cascade'
    )
    criteria_id = fields.Many2one(
        'hr.evaluation.criteria',
        string='Critère',
        required=True
    )
    score = fields.Float(
        string='Score',
        default=0.0,
        help="Score de 0 à 5"
    )
    weight = fields.Float(
        string='Poids',
        default=1.0,
        help="Poids du critère dans l'évaluation globale"
    )
    comments = fields.Text(string='Commentaires')
    
    # Champs liés
    criteria_name = fields.Char(
        related='criteria_id.name',
        string='Nom du critère'
    )
    criteria_description = fields.Text(
        related='criteria_id.description',
        string='Description'
    )
    
    @api.constrains('score')
    def _check_score(self):
        for record in self:
            if record.score < 0 or record.score > 5:
                raise ValidationError(_("Le score doit être compris entre 0 et 5."))
