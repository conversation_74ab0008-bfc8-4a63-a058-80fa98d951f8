# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError
from datetime import datetime, timedelta


class HrEvaluationWizard(models.TransientModel):
    _name = 'hr.evaluation.wizard'
    _description = 'Assistant de création d\'évaluation'

    # Sélection des employés
    employee_ids = fields.Many2many(
        'hr.employee',
        string='Employés à évaluer',
        required=True
    )
    department_ids = fields.Many2many(
        'hr.department',
        string='Départements'
    )
    
    # Configuration de l'évaluation
    template_id = fields.Many2one(
        'hr.evaluation.template',
        string='Modèle d\'évaluation',
        required=True
    )
    evaluation_type = fields.Selection([
        ('annual', 'Annuelle'),
        ('mid_year', 'Mi-année'),
        ('quarterly', 'Trimestrielle'),
        ('probation', 'Période d\'essai'),
        ('project', 'Fin de projet'),
        ('360', 'Évaluation 360°')
    ], string='Type d\'évaluation', required=True, default='annual')
    
    # Dates
    evaluation_date = fields.Date(
        string='Date d\'évaluation',
        required=True,
        default=fields.Date.today
    )
    period_start = fields.Date(
        string='Début de période',
        required=True
    )
    period_end = fields.Date(
        string='Fin de période',
        required=True
    )
    
    # Options
    auto_start = fields.Boolean(
        string='Démarrer automatiquement',
        default=True,
        help="Démarre automatiquement le processus d'évaluation"
    )
    send_notification = fields.Boolean(
        string='Envoyer des notifications',
        default=True,
        help="Envoie des notifications aux employés et managers"
    )
    
    @api.onchange('department_ids')
    def _onchange_department_ids(self):
        """Filtre les employés par département"""
        if self.department_ids:
            employees = self.env['hr.employee'].search([
                ('department_id', 'in', self.department_ids.ids)
            ])
            self.employee_ids = employees
    
    @api.onchange('evaluation_type')
    def _onchange_evaluation_type(self):
        """Met à jour les dates selon le type d'évaluation"""
        today = fields.Date.today()
        
        if self.evaluation_type == 'annual':
            self.period_start = today.replace(month=1, day=1)
            self.period_end = today.replace(month=12, day=31)
        elif self.evaluation_type == 'mid_year':
            if today.month <= 6:
                self.period_start = today.replace(month=1, day=1)
                self.period_end = today.replace(month=6, day=30)
            else:
                self.period_start = today.replace(month=7, day=1)
                self.period_end = today.replace(month=12, day=31)
        elif self.evaluation_type == 'quarterly':
            quarter = (today.month - 1) // 3 + 1
            start_month = (quarter - 1) * 3 + 1
            end_month = quarter * 3
            self.period_start = today.replace(month=start_month, day=1)
            if end_month == 12:
                self.period_end = today.replace(month=12, day=31)
            else:
                next_month = today.replace(month=end_month + 1, day=1)
                self.period_end = next_month - timedelta(days=1)
    
    def action_create_evaluations(self):
        """Crée les évaluations pour les employés sélectionnés"""
        if not self.employee_ids:
            raise UserError(_("Veuillez sélectionner au moins un employé."))
        
        evaluations = self.env['hr.evaluation']
        
        for employee in self.employee_ids:
            # Vérification des évaluations existantes
            existing = self.env['hr.evaluation'].search([
                ('employee_id', '=', employee.id),
                ('evaluation_type', '=', self.evaluation_type),
                ('period_start', '=', self.period_start),
                ('period_end', '=', self.period_end),
                ('state', '!=', 'cancelled')
            ])
            
            if existing:
                continue  # Skip si évaluation existe déjà
            
            # Création de l'évaluation
            evaluation_vals = {
                'name': f"Évaluation {self.evaluation_type} - {employee.name}",
                'employee_id': employee.id,
                'evaluator_id': employee.parent_id.id if employee.parent_id else False,
                'template_id': self.template_id.id,
                'evaluation_type': self.evaluation_type,
                'evaluation_date': self.evaluation_date,
                'period_start': self.period_start,
                'period_end': self.period_end,
            }
            
            evaluation = self.env['hr.evaluation'].create(evaluation_vals)
            evaluations |= evaluation
            
            # Démarrage automatique si demandé
            if self.auto_start and self.template_id.allow_self_evaluation:
                evaluation.action_start_self_evaluation()
        
        # Notification
        if self.send_notification:
            self._send_notifications(evaluations)
        
        # Retour vers la liste des évaluations créées
        return {
            'name': _('Évaluations créées'),
            'type': 'ir.actions.act_window',
            'res_model': 'hr.evaluation',
            'view_mode': 'tree,form',
            'domain': [('id', 'in', evaluations.ids)],
            'context': {'create': False}
        }
    
    def _send_notifications(self, evaluations):
        """Envoie les notifications pour les évaluations créées"""
        template = self.env.ref('hr_objectives_evaluation.mail_template_evaluation_created', False)
        
        if template:
            for evaluation in evaluations:
                template.send_mail(evaluation.id, force_send=True)


class HrObjectiveBulkWizard(models.TransientModel):
    _name = 'hr.objective.bulk.wizard'
    _description = 'Assistant de création d\'objectifs en masse'

    # Sélection des employés
    employee_ids = fields.Many2many(
        'hr.employee',
        string='Employés',
        required=True
    )
    department_ids = fields.Many2many(
        'hr.department',
        string='Départements'
    )
    
    # Configuration de l'objectif
    name = fields.Char(
        string='Nom de l\'objectif',
        required=True
    )
    description = fields.Html(
        string='Description'
    )
    
    # Dates
    start_date = fields.Date(
        string='Date de début',
        required=True,
        default=fields.Date.today
    )
    deadline = fields.Date(
        string='Date limite',
        required=True
    )
    
    # Configuration
    priority = fields.Selection([
        ('0', 'Faible'),
        ('1', 'Normal'),
        ('2', 'Élevé'),
        ('3', 'Urgent')
    ], string='Priorité', default='1')
    
    target_value = fields.Float(string='Valeur cible')
    unit = fields.Char(string='Unité de mesure')
    
    @api.onchange('department_ids')
    def _onchange_department_ids(self):
        """Filtre les employés par département"""
        if self.department_ids:
            employees = self.env['hr.employee'].search([
                ('department_id', 'in', self.department_ids.ids)
            ])
            self.employee_ids = employees
    
    def action_create_objectives(self):
        """Crée les objectifs pour les employés sélectionnés"""
        if not self.employee_ids:
            raise UserError(_("Veuillez sélectionner au moins un employé."))
        
        objectives = self.env['hr.objective']
        
        for employee in self.employee_ids:
            objective_vals = {
                'name': self.name,
                'description': self.description,
                'employee_id': employee.id,
                'start_date': self.start_date,
                'deadline': self.deadline,
                'priority': self.priority,
                'target_value': self.target_value,
                'unit': self.unit,
            }
            
            objective = self.env['hr.objective'].create(objective_vals)
            objectives |= objective
        
        # Retour vers la liste des objectifs créés
        return {
            'name': _('Objectifs créés'),
            'type': 'ir.actions.act_window',
            'res_model': 'hr.objective',
            'view_mode': 'tree,form',
            'domain': [('id', 'in', objectives.ids)],
            'context': {'create': False}
        }
