<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Vue Tree des Objectifs -->
    <record id="view_hr_objective_tree" model="ir.ui.view">
        <field name="name">hr.objective.tree</field>
        <field name="model">hr.objective</field>
        <field name="type">tree</field>
        <field name="arch" type="xml">
            <tree decoration-danger="is_overdue" decoration-success="state=='achieved'" decoration-muted="state=='cancelled'">
                <field name="name"/>
                <field name="employee_id"/>
                <field name="department_id"/>
                <field name="start_date"/>
                <field name="deadline"/>
                <field name="state" widget="badge" decoration-success="state=='achieved'" decoration-warning="state=='in_progress'" decoration-danger="state=='not_achieved'"/>
                <field name="progress" widget="progressbar"/>
                <field name="priority" widget="priority"/>
                <field name="days_remaining"/>
                <field name="is_overdue" invisible="1"/>
            </tree>
        </field>
    </record>

    <!-- Vue Form des Objectifs -->
    <record id="view_hr_objective_form" model="ir.ui.view">
        <field name="name">hr.objective.form</field>
        <field name="model">hr.objective</field>
        <field name="type">form</field>
        <field name="arch" type="xml">
            <form>
                <header>
                    <button name="action_start" string="Démarrer" type="object" 
                            attrs="{'invisible': [('state', '!=', 'draft')]}" class="btn-primary"/>
                    <button name="action_achieve" string="Marquer comme atteint" type="object" 
                            attrs="{'invisible': [('state', 'not in', ['in_progress'])]}" class="btn-success"/>
                    <button name="action_cancel" string="Annuler" type="object" 
                            attrs="{'invisible': [('state', 'in', ['achieved', 'cancelled'])]}" class="btn-secondary"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,in_progress,achieved"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_evaluations" type="object" class="oe_stat_button" icon="fa-star">
                            <field name="evaluation_ids" widget="statinfo" string="Évaluations"/>
                        </button>
                    </div>
                    
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Nom de l'objectif"/>
                        </h1>
                    </div>
                    
                    <group>
                        <group>
                            <field name="employee_id"/>
                            <field name="manager_id" readonly="1"/>
                            <field name="department_id" readonly="1"/>
                            <field name="priority"/>
                        </group>
                        <group>
                            <field name="start_date"/>
                            <field name="deadline"/>
                            <field name="days_remaining" readonly="1"/>
                            <field name="is_overdue" readonly="1"/>
                        </group>
                    </group>
                    
                    <notebook>
                        <page string="Description">
                            <field name="description" widget="html"/>
                        </page>
                        
                        <page string="Critères SMART">
                            <group>
                                <field name="specific" string="Spécifique"/>
                                <field name="measurable" string="Mesurable"/>
                                <field name="achievable" string="Atteignable"/>
                                <field name="relevant" string="Pertinent"/>
                                <field name="time_bound" string="Temporellement défini"/>
                            </group>
                        </page>
                        
                        <page string="Suivi">
                            <group>
                                <group>
                                    <field name="progress" widget="progressbar"/>
                                    <field name="target_value"/>
                                    <field name="current_value"/>
                                    <field name="unit"/>
                                </group>
                                <group>
                                    <field name="achievement_rate" readonly="1" widget="progressbar"/>
                                </group>
                            </group>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <!-- Vue Kanban des Objectifs -->
    <record id="view_hr_objective_kanban" model="ir.ui.view">
        <field name="name">hr.objective.kanban</field>
        <field name="model">hr.objective</field>
        <field name="type">kanban</field>
        <field name="arch" type="xml">
            <kanban default_group_by="state" class="o_kanban_small_column">
                <field name="name"/>
                <field name="employee_id"/>
                <field name="deadline"/>
                <field name="progress"/>
                <field name="priority"/>
                <field name="state"/>
                <field name="is_overdue"/>
                <templates>
                    <t t-name="kanban-box">
                        <div t-attf-class="oe_kanban_card oe_kanban_global_click #{record.is_overdue.raw_value ? 'oe_kanban_color_2' : ''}">
                            <div class="oe_kanban_content">
                                <div class="o_kanban_record_top">
                                    <div class="o_kanban_record_headings">
                                        <strong class="o_kanban_record_title">
                                            <field name="name"/>
                                        </strong>
                                    </div>
                                    <div class="o_kanban_record_top_right">
                                        <field name="priority" widget="priority"/>
                                    </div>
                                </div>
                                <div class="o_kanban_record_body">
                                    <field name="employee_id"/>
                                    <div class="o_kanban_record_bottom">
                                        <div class="oe_kanban_bottom_left">
                                            <field name="deadline"/>
                                        </div>
                                        <div class="oe_kanban_bottom_right">
                                            <field name="progress" widget="progressbar"/>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <!-- Vue Search des Objectifs -->
    <record id="view_hr_objective_search" model="ir.ui.view">
        <field name="name">hr.objective.search</field>
        <field name="model">hr.objective</field>
        <field name="type">search</field>
        <field name="arch" type="xml">
            <search>
                <field name="name"/>
                <field name="employee_id"/>
                <field name="department_id"/>
                <field name="manager_id"/>
                <separator/>
                <filter string="Mes objectifs" name="my_objectives" 
                        domain="[('employee_id.user_id', '=', uid)]"/>
                <filter string="Objectifs de mon équipe" name="team_objectives" 
                        domain="[('manager_id.user_id', '=', uid)]"/>
                <separator/>
                <filter string="En cours" name="in_progress" 
                        domain="[('state', '=', 'in_progress')]"/>
                <filter string="En retard" name="overdue" 
                        domain="[('is_overdue', '=', True)]"/>
                <filter string="Atteints" name="achieved" 
                        domain="[('state', '=', 'achieved')]"/>
                <separator/>
                <group expand="0" string="Grouper par">
                    <filter string="Employé" name="group_employee" context="{'group_by': 'employee_id'}"/>
                    <filter string="Département" name="group_department" context="{'group_by': 'department_id'}"/>
                    <filter string="État" name="group_state" context="{'group_by': 'state'}"/>
                    <filter string="Priorité" name="group_priority" context="{'group_by': 'priority'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Action pour les Objectifs -->
    <record id="action_hr_objective" model="ir.actions.act_window">
        <field name="name">Objectifs</field>
        <field name="res_model">hr.objective</field>
        <field name="view_mode">kanban,tree,form</field>
        <field name="search_view_id" ref="view_hr_objective_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Créer un nouvel objectif
            </p>
            <p>
                Définissez des objectifs SMART pour vos employés et suivez leur progression.
            </p>
        </field>
    </record>
</odoo>
