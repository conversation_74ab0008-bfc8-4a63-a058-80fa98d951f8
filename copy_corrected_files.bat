@echo off
echo ========================================
echo COPIE DES FICHIERS CORRIGES VERS ODOO
echo ========================================

echo.
echo Copie des fichiers de vues corriges...

echo - hr_objective_views.xml
copy "hr_objectives_evaluation\views\hr_objective_views.xml" "C:\Program Files\Odoo 18.0.20250709\server\odoo\addons\hr_objectives_evaluation\views\hr_objective_views.xml"

echo - hr_evaluation_views.xml
copy "hr_objectives_evaluation\views\hr_evaluation_views.xml" "C:\Program Files\Odoo 18.0.20250709\server\odoo\addons\hr_objectives_evaluation\views\hr_evaluation_views.xml"

echo - hr_evaluation_criteria_views.xml
copy "hr_objectives_evaluation\views\hr_evaluation_criteria_views.xml" "C:\Program Files\Odoo 18.0.20250709\server\odoo\addons\hr_objectives_evaluation\views\hr_evaluation_criteria_views.xml"

echo - hr_evaluation_template_views.xml
copy "hr_objectives_evaluation\views\hr_evaluation_template_views.xml" "C:\Program Files\Odoo 18.0.20250709\server\odoo\addons\hr_objectives_evaluation\views\hr_evaluation_template_views.xml"

echo - hr_wizard_views.xml
copy "hr_objectives_evaluation\views\hr_wizard_views.xml" "C:\Program Files\Odoo 18.0.20250709\server\odoo\addons\hr_objectives_evaluation\views\hr_wizard_views.xml"

echo - hr_objective_wizard.py (corrige)
copy "hr_objectives_evaluation\wizards\hr_objective_wizard.py" "C:\Program Files\Odoo 18.0.20250709\server\odoo\addons\hr_objectives_evaluation\wizards\hr_objective_wizard.py"

echo.
echo ========================================
echo FICHIERS COPIES AVEC SUCCES !
echo ========================================
echo.
echo Maintenant redemarrez le service Odoo :
echo   net stop odoo-server-18.0
echo   net start odoo-server-18.0
echo.
pause
