<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Vue Form pour l'assistant de création d'évaluations -->
    <record id="view_hr_evaluation_wizard_form" model="ir.ui.view">
        <field name="name">hr.evaluation.wizard.form</field>
        <field name="model">hr.evaluation.wizard</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <div class="oe_title">
                        <h1>Assistant de Création d'Évaluations</h1>
                    </div>
                    
                    <group>
                        <group string="Sélection des Employés">
                            <field name="department_ids" widget="many2many_tags"/>
                            <field name="employee_ids" widget="many2many_tags"/>
                        </group>
                        <group string="Configuration">
                            <field name="template_id"/>
                            <field name="evaluation_type"/>
                            <field name="evaluation_date"/>
                        </group>
                    </group>
                    
                    <group>
                        <group string="Période d'Évaluation">
                            <field name="period_start"/>
                            <field name="period_end"/>
                        </group>
                        <group string="Options">
                            <field name="auto_start"/>
                            <field name="send_notification"/>
                        </group>
                    </group>
                </sheet>
                <footer>
                    <button name="action_create_evaluations" string="Créer les Évaluations" type="object" class="btn-primary"/>
                    <button string="Annuler" class="btn-secondary" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>

    <!-- Action pour l'assistant de création d'évaluations -->
    <record id="action_hr_evaluation_wizard" model="ir.actions.act_window">
        <field name="name">Créer des Évaluations</field>
        <field name="res_model">hr.evaluation.wizard</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
    </record>

    <!-- Vue Form pour l'assistant de création d'objectifs en masse -->
    <record id="view_hr_objective_bulk_wizard_form" model="ir.ui.view">
        <field name="name">hr.objective.bulk.wizard.form</field>
        <field name="model">hr.objective.bulk.wizard</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <div class="oe_title">
                        <h1>Création d'Objectifs en Masse</h1>
                    </div>
                    
                    <group>
                        <group string="Sélection des Employés">
                            <field name="department_ids" widget="many2many_tags"/>
                            <field name="employee_ids" widget="many2many_tags"/>
                        </group>
                        <group string="Configuration">
                            <field name="priority"/>
                            <field name="target_value"/>
                            <field name="unit"/>
                        </group>
                    </group>
                    
                    <group>
                        <group string="Dates">
                            <field name="start_date"/>
                            <field name="deadline"/>
                        </group>
                    </group>
                    
                    <group string="Objectif">
                        <field name="name"/>
                        <field name="description" widget="html"/>
                    </group>
                </sheet>
                <footer>
                    <button name="action_create_objectives" string="Créer les Objectifs" type="object" class="btn-primary"/>
                    <button string="Annuler" class="btn-secondary" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>

    <!-- Action pour l'assistant de création d'objectifs en masse -->
    <record id="action_hr_objective_bulk_wizard" model="ir.actions.act_window">
        <field name="name">Créer des Objectifs en Masse</field>
        <field name="res_model">hr.objective.bulk.wizard</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
    </record>

    <!-- Vue Form pour l'assistant de mise à jour de progression -->
    <record id="view_hr_objective_progress_wizard_form" model="ir.ui.view">
        <field name="name">hr.objective.progress.wizard.form</field>
        <field name="model">hr.objective.progress.wizard</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <div class="oe_title">
                        <h1>Mise à Jour de la Progression</h1>
                    </div>
                    
                    <group>
                        <group>
                            <field name="objective_id" readonly="1"/>
                            <field name="current_progress" widget="progressbar"/>
                        </group>
                        <group>
                            <field name="new_progress"/>
                            <field name="current_value"/>
                        </group>
                    </group>
                    
                    <group>
                        <field name="comments" placeholder="Commentaires sur cette mise à jour..."/>
                    </group>
                </sheet>
                <footer>
                    <button name="action_update_progress" string="Mettre à Jour" type="object" class="btn-primary"/>
                    <button string="Annuler" class="btn-secondary" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>

    <!-- Vue Form pour l'assistant de rapport d'objectifs -->
    <record id="view_hr_objective_report_wizard_form" model="ir.ui.view">
        <field name="name">hr.objective.report.wizard.form</field>
        <field name="model">hr.objective.report.wizard</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <div class="oe_title">
                        <h1>Rapport d'Objectifs</h1>
                    </div>
                    
                    <group>
                        <group string="Filtres">
                            <field name="employee_ids" widget="many2many_tags"/>
                            <field name="department_ids" widget="many2many_tags"/>
                            <field name="manager_ids" widget="many2many_tags"/>
                        </group>
                        <group string="Options">
                            <field name="state_filter"/>
                            <field name="group_by"/>
                        </group>
                    </group>
                    
                    <group>
                        <group string="Période">
                            <field name="date_from"/>
                            <field name="date_to"/>
                        </group>
                    </group>
                </sheet>
                <footer>
                    <button name="action_generate_report" string="Générer le Rapport" type="object" class="btn-primary"/>
                    <button string="Annuler" class="btn-secondary" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>

    <!-- Action pour l'assistant de rapport d'objectifs -->
    <record id="action_hr_objective_report_wizard" model="ir.actions.act_window">
        <field name="name">Rapport d'Objectifs</field>
        <field name="res_model">hr.objective.report.wizard</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
    </record>
</odoo>
