# -*- coding: utf-8 -*-

from odoo import http, _
from odoo.http import request
from odoo.addons.portal.controllers.portal import CustomerPortal, pager as portal_pager
from odoo.exceptions import AccessError, MissingError
from collections import OrderedDict
from odoo.tools import groupby as groupbyelem
from operator import itemgetter


class HrObjectivesPortal(CustomerPortal):

    def _prepare_home_portal_values(self, counters):
        """Ajouter les compteurs d'objectifs et évaluations au portail"""
        values = super()._prepare_home_portal_values(counters)
        
        user = request.env.user
        employee = request.env['hr.employee'].search([('user_id', '=', user.id)], limit=1)
        
        if employee:
            if 'objective_count' in counters:
                values['objective_count'] = request.env['hr.objective'].search_count([
                    ('employee_id', '=', employee.id)
                ])
            
            if 'evaluation_count' in counters:
                values['evaluation_count'] = request.env['hr.evaluation'].search_count([
                    ('employee_id', '=', employee.id)
                ])
        
        return values

    def _objective_get_page_view_values(self, objective, access_token, **kwargs):
        """Préparer les valeurs pour la vue détaillée d'un objectif"""
        values = {
            'page_name': 'objective',
            'objective': objective,
        }
        return self._get_page_view_values(objective, access_token, values, 'my_objectives_history', False, **kwargs)

    @http.route(['/my/objectives', '/my/objectives/page/<int:page>'], type='http', auth="user", website=True)
    def portal_my_objectives(self, page=1, date_begin=None, date_end=None, sortby=None, filterby=None, **kw):
        """Page des objectifs dans le portail"""
        user = request.env.user
        employee = request.env['hr.employee'].search([('user_id', '=', user.id)], limit=1)
        
        if not employee:
            return request.render('hr_objectives_evaluation.portal_no_employee')
        
        values = self._prepare_portal_layout_values()
        
        # Domaine de recherche
        domain = [('employee_id', '=', employee.id)]
        
        # Filtres
        searchbar_filters = {
            'all': {'label': _('Tous'), 'domain': []},
            'draft': {'label': _('Brouillon'), 'domain': [('state', '=', 'draft')]},
            'in_progress': {'label': _('En cours'), 'domain': [('state', '=', 'in_progress')]},
            'achieved': {'label': _('Atteints'), 'domain': [('state', '=', 'achieved')]},
            'overdue': {'label': _('En retard'), 'domain': [('is_overdue', '=', True)]},
        }
        
        if not filterby:
            filterby = 'all'
        domain += searchbar_filters[filterby]['domain']
        
        # Tri
        searchbar_sortings = {
            'date': {'label': _('Date limite'), 'order': 'deadline desc'},
            'name': {'label': _('Nom'), 'order': 'name'},
            'state': {'label': _('État'), 'order': 'state'},
            'progress': {'label': _('Progression'), 'order': 'progress desc'},
        }
        
        if not sortby:
            sortby = 'date'
        order = searchbar_sortings[sortby]['order']
        
        # Pagination
        objective_count = request.env['hr.objective'].search_count(domain)
        pager = portal_pager(
            url="/my/objectives",
            url_args={'date_begin': date_begin, 'date_end': date_end, 'sortby': sortby, 'filterby': filterby},
            total=objective_count,
            page=page,
            step=self._items_per_page
        )
        
        # Récupération des objectifs
        objectives = request.env['hr.objective'].search(
            domain, order=order, limit=self._items_per_page, offset=pager['offset']
        )
        
        values.update({
            'date': date_begin,
            'date_end': date_end,
            'objectives': objectives,
            'page_name': 'objective',
            'default_url': '/my/objectives',
            'pager': pager,
            'searchbar_sortings': searchbar_sortings,
            'searchbar_filters': OrderedDict(sorted(searchbar_filters.items())),
            'sortby': sortby,
            'filterby': filterby,
        })
        
        return request.render("hr_objectives_evaluation.portal_my_objectives", values)

    @http.route(['/my/objective/<int:objective_id>'], type='http', auth="user", website=True)
    def portal_my_objective_detail(self, objective_id, access_token=None, **kw):
        """Page de détail d'un objectif"""
        try:
            objective_sudo = self._document_check_access('hr.objective', objective_id, access_token)
        except (AccessError, MissingError):
            return request.redirect('/my')
        
        values = self._objective_get_page_view_values(objective_sudo, access_token, **kw)
        return request.render("hr_objectives_evaluation.portal_objective_page", values)

    @http.route(['/my/evaluations', '/my/evaluations/page/<int:page>'], type='http', auth="user", website=True)
    def portal_my_evaluations(self, page=1, date_begin=None, date_end=None, sortby=None, filterby=None, **kw):
        """Page des évaluations dans le portail"""
        user = request.env.user
        employee = request.env['hr.employee'].search([('user_id', '=', user.id)], limit=1)
        
        if not employee:
            return request.render('hr_objectives_evaluation.portal_no_employee')
        
        values = self._prepare_portal_layout_values()
        
        # Domaine de recherche
        domain = [('employee_id', '=', employee.id)]
        
        # Filtres
        searchbar_filters = {
            'all': {'label': _('Toutes'), 'domain': []},
            'draft': {'label': _('Brouillon'), 'domain': [('state', '=', 'draft')]},
            'self_evaluation': {'label': _('Auto-évaluation'), 'domain': [('state', '=', 'self_evaluation')]},
            'completed': {'label': _('Terminées'), 'domain': [('state', '=', 'completed')]},
        }
        
        if not filterby:
            filterby = 'all'
        domain += searchbar_filters[filterby]['domain']
        
        # Tri
        searchbar_sortings = {
            'date': {'label': _('Date d\'évaluation'), 'order': 'evaluation_date desc'},
            'name': {'label': _('Nom'), 'order': 'name'},
            'type': {'label': _('Type'), 'order': 'evaluation_type'},
            'score': {'label': _('Score'), 'order': 'overall_score desc'},
        }
        
        if not sortby:
            sortby = 'date'
        order = searchbar_sortings[sortby]['order']
        
        # Pagination
        evaluation_count = request.env['hr.evaluation'].search_count(domain)
        pager = portal_pager(
            url="/my/evaluations",
            url_args={'date_begin': date_begin, 'date_end': date_end, 'sortby': sortby, 'filterby': filterby},
            total=evaluation_count,
            page=page,
            step=self._items_per_page
        )
        
        # Récupération des évaluations
        evaluations = request.env['hr.evaluation'].search(
            domain, order=order, limit=self._items_per_page, offset=pager['offset']
        )
        
        values.update({
            'date': date_begin,
            'date_end': date_end,
            'evaluations': evaluations,
            'page_name': 'evaluation',
            'default_url': '/my/evaluations',
            'pager': pager,
            'searchbar_sortings': searchbar_sortings,
            'searchbar_filters': OrderedDict(sorted(searchbar_filters.items())),
            'sortby': sortby,
            'filterby': filterby,
        })
        
        return request.render("hr_objectives_evaluation.portal_my_evaluations", values)

    @http.route(['/my/evaluation/<int:evaluation_id>'], type='http', auth="user", website=True)
    def portal_my_evaluation_detail(self, evaluation_id, access_token=None, **kw):
        """Page de détail d'une évaluation"""
        try:
            evaluation_sudo = self._document_check_access('hr.evaluation', evaluation_id, access_token)
        except (AccessError, MissingError):
            return request.redirect('/my')
        
        values = {
            'page_name': 'evaluation',
            'evaluation': evaluation_sudo,
        }
        
        return request.render("hr_objectives_evaluation.portal_evaluation_page", values)

    @http.route('/my/evaluation/<int:evaluation_id>/self_evaluate', type='http', auth="user", website=True, methods=['GET', 'POST'])
    def portal_self_evaluation(self, evaluation_id, access_token=None, **kw):
        """Formulaire d'auto-évaluation"""
        try:
            evaluation_sudo = self._document_check_access('hr.evaluation', evaluation_id, access_token)
        except (AccessError, MissingError):
            return request.redirect('/my')
        
        if evaluation_sudo.state != 'self_evaluation':
            return request.redirect(f'/my/evaluation/{evaluation_id}')
        
        if request.httprequest.method == 'POST':
            # Traitement de la soumission
            for key, value in kw.items():
                if key.startswith('score_'):
                    criteria_line_id = int(key.replace('score_', ''))
                    criteria_line = evaluation_sudo.criteria_line_ids.filtered(
                        lambda l: l.id == criteria_line_id
                    )
                    if criteria_line:
                        criteria_line.sudo().write({'score': float(value)})
                
                elif key.startswith('comment_'):
                    criteria_line_id = int(key.replace('comment_', ''))
                    criteria_line = evaluation_sudo.criteria_line_ids.filtered(
                        lambda l: l.id == criteria_line_id
                    )
                    if criteria_line:
                        criteria_line.sudo().write({'comments': value})
            
            # Mise à jour des commentaires généraux
            evaluation_sudo.sudo().write({
                'employee_comments': kw.get('employee_comments', ''),
                'self_evaluation_score': evaluation_sudo.overall_score,
            })
            
            # Soumission
            evaluation_sudo.sudo().action_submit_self_evaluation()
            
            return request.redirect(f'/my/evaluation/{evaluation_id}?message=submitted')
        
        values = {
            'page_name': 'self_evaluation',
            'evaluation': evaluation_sudo,
        }
        
        return request.render("hr_objectives_evaluation.portal_self_evaluation", values)
